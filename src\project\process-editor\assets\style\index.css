.aoa3 {
  --theme-color: #119078;
  .ant-modal-content {
    background-color: #e1f3f1;
  }
  .ant-modal-header {
    background-color: #e1f3f1;
  }
  .vben-icon-button:hover {
    background-color: rgba(17, 144, 120, 0.16);
  }
  .ant-input[disabled] {
    background-color: #c9ede7 !important;
  }
  .ant-input:hover,
  .ant-input-number:hover {
    border-color: #2c9c83 !important;
  }

  .ant-input-number:focus,
  .ant-input-number-focused {
    box-shadow: 0 0 0 2px rgba(5, 70, 47, 0.26);
    border-color: #2c9c83;
  }

  .ant-btn-primary {
    color: #fff !important;
    background-color: var(--theme-color) !important;
  }

  .ant-btn-primary:not([disabled]):hover {
    background-color: #119078 !important;
  }

  .ant-select-selector {
    border-color: #2c9c83;
  }

  .ant-select {
    &.ant-select-focused:not(.ant-select-status-error) {
      .ant-select-selector {
        border-color: #2c9c83 !important;
        box-shadow: 0 0 0 2px rgba(5, 70, 47, 0.26) !important;
      }
    }
    &:not(.ant-select-disabled):not(.ant-select-status-error):not(
        .ant-pagination-size-changer
      ):hover {
      .ant-select-selector {
        border-color: #2c9c83;
      }
    }
  }
  &.ant-select-dropdown {
    background-color: #e1f3f1;
    border: 1px solid #94dcd5;
    box-shadow: 0 1px 8px #577572;
    .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
      background-color: #c0e3dd;
    }
    .ant-select-item-option-selected {
      background-color: var(--theme-color) !important;
      color: #fff !important;
    }
  }

  .ant-tooltip .ant-tooltip-inner {
    background: linear-gradient(to bottom, #015848, #025849);
    box-shadow: 0 4px 6px #1d2e1e;
    border: 1px solid #9ec5bf;
  }
}
