.ant-picker {
  width: 100% !important;

  &.ant-picker-disabled {
    background-color: #f2f3f5 !important;
  }

  &-dropdown {
    .font-size(14);

    .ant-picker-range-arrow {
      &::before {
        background: @aoa3-join-from-bg;
      }
    }

    .ant-picker-header {
      & > button {
        .font-size(14);
        .px2vw(40);

        line-height: @vw;
      }
    }

    .ant-picker-super-prev-icon,
    .ant-picker-super-next-icon {
      .px2vw(7);
      .width-prop(7);

      height: @vw;

      &::before,
      &::after {
        .px2vw(7);
        .width-prop(7);

        height: @vw;
      }
    }

    .ant-picker-header-view {
      .px2vw(40);

      height: @vw;

      button {
        line-height: @vw;
      }
    }

    .ant-picker-month-panel {
      .width-prop(280);

      .ant-picker-content {
        .px2vw(264);

        height: @vw;
      }

      .ant-picker-cell {
        .px2vw(4);

        padding: @vw 0;

        &::before {
          .px2vw(24);

          height: @vw;
        }
      }

      .ant-picker-cell-inner {
        .width-prop(60);
        .px2vw(24);

        line-height: @vw;
        height: @vw;
      }
    }
  }
}
