<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    destroyOnClose
    :title="title"
    @cancel="handleCancel"
    width="50%"
    :maskClosable="false"
    wrapClassName="ai-analysis-modal"
  >
    <!-- <template #extra>
      <IconButton @click="drawerCloseHandler" color="#666666" icon="icon-park-outline:close" />
    </template> -->
    <AiDeepAnalysis
      v-bind="{ loading, aiQuestion, aiData: aiDataValue, duration }"
      @setScroll="setScroll"
    />
    <template #footer>
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, onUnmounted, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { AiDeepAnalysis } from '/@aoa/components/AiAnalysis';
  import { getAiAnalysisApi, getStreamAiAnalysisApi } from '/@aoa/api/ai';
  import { useMessage } from '/@/hooks/web/useMessage';

  const props = defineProps({
    title: {
      type: String,
      default: 'AI分析',
    },
    //是否流式请求
    stream: {
      type: Boolean,
      default: true,
    },
    //模型类型
    modelName: {
      type: String,
      default: 'deepseek-reasoner',
    },
    aiQuestion: {
      type: String,
      default: '',
    },
  });

  const loading = ref(false);
  let aiDataValue = reactive({
    deepAnalysis: '',
    analysisContent: '',
  });
  const duration = ref(0);
  const chatController = ref<AbortController>(new AbortController());
  let observer: MutationObserver | null = null;
  const containerRef = ref();

  const { createMessage } = useMessage();

  const [registerModal, { closeModal }] = useModalInner(async () => {
    aiDataValue.deepAnalysis = '';
    aiDataValue.analysisContent = '';
    const chatControllerTemp = new AbortController();
    chatController.value = chatControllerTemp;

    const params = Object.assign({
      modelName: props.modelName,
      stream: props.stream,
      content: props.aiQuestion,
      // content: '你好',
    });
    containerRef.value = document.querySelector('.ai-analysis-modal .warp-box');
    observeContentChanges();
    const fun = props.stream ? getAiAnalysisStream : getAiAnalysis;

    await fun(params);
  });

  // 获取AI分析
  const getAiAnalysis = async (params) => {
    loading.value = true;
    const startTime = performance.now();
    const { message } = await getAiAnalysisApi(params);
    const endTime = performance.now();
    duration.value = endTime - startTime;
    aiDataValue.deepAnalysis = message.reasoning_content;
    aiDataValue.analysisContent = message.content;
    loading.value = false;
  };

  // 生成中
  const generatingMessage = ({ reasoningContent, content }) => {
    if (reasoningContent) {
      aiDataValue.deepAnalysis = reasoningContent;
    }
    if (content) {
      aiDataValue.analysisContent = content;
    }
  };

  // 流式请求
  const getAiAnalysisStream = async (params) => {
    loading.value = true;

    try {
      const startTime = performance.now();
      const data = await getStreamAiAnalysisApi({
        params,
        onMessage: generatingMessage,
        abortCtrl: chatController.value,
      });
      const { content, reasoningContent } = data;
      if (content && reasoningContent) {
        loading.value = false;
        const endTime = performance.now();
        duration.value = endTime - startTime;
      }

      console.log('请求结束data', data);
    } catch (error) {
      if (error instanceof Error) {
        createMessage.error(error.message || '未知错误');
      } else {
        // 如果 error 不是 Error 实例，提供默认错误信息
        createMessage.error('发生未知错误，请稍后再试');
      }
    }
  };
  //  关闭modal
  const handleCancel = () => {
    closeModal();
    loading.value = false;
    aiDataValue.deepAnalysis = '';
    aiDataValue.analysisContent = '';
    chatController.value.abort();
  };

  const setScroll = () => {
    document.querySelector('.ai-analysis-modal .ant-modal-content')?.classList.add('scroll');
  };

  const scrollToBottom = () => {
    nextTick(() => {
      if (containerRef.value) {
        containerRef.value.scrollTo({
          top: containerRef.value.scrollHeight,
          behavior: 'smooth',
        });
      }
    });
  };

  const observeContentChanges = () => {
    console.log('observeContentChanges', containerRef);
    if (!containerRef.value) return;

    observer = new MutationObserver(() => {
      scrollToBottom();
    });

    observer.observe(containerRef.value, {
      childList: true,
      subtree: true,
      attributes: false,
    });
  };

  const disconnectObserver = () => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  };

  onUnmounted(() => {
    loading.value = false;
    aiDataValue.deepAnalysis = '';
    aiDataValue.analysisContent = '';
    chatController.value.abort();
    disconnectObserver();
  });
</script>
<style lang="less">
  .ai-analysis-modal {
    .ant-modal {
      height: 70%;

      & > div {
        height: 100%;
      }

      .ant-modal-content {
        height: 100%;
        background: linear-gradient(180deg, #015848 0%, #025849 100%);
        box-shadow: 0px 4px 6px 0px #1d2e1e;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #9ec5bf;

        .ant-modal-close {
          .width-prop(16, top) !important;

          .vben-icon-button {
            .width-prop(32);
            .width-prop(32, height);
          }

          .app-iconify {
            color: #fff !important;
          }
        }

        &.scroll {
          .ant-modal-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.24);
          }

          .ant-modal-footer {
            border-top: 1px solid rgba(255, 255, 255, 0.24);
          }
        }

        .ant-modal-header {
          .width-prop(64, height);
          background: linear-gradient(180deg, #015848 0%, #025849 100%);

          .ant-modal-title {
            .vben-basic-title {
              .font-size(18);
              color: #fff;
            }
          }
        }

        .ant-modal-body {
          .px2vw(144);
          height: calc(100% - @vw);

          .scrollbar__bar {
            display: none;
          }

          .scrollbar__view {
            height: 100%;

            .warp-box {
              max-height: calc(100%) !important;
              overflow-y: overlay;
              scroll-behavior: smooth;

              &::-webkit-scrollbar-corner {
                background: #6fa097 !important;
              }

              &::-webkit-scrollbar-track {
                background: #2a7365 !important;
              }
            }
          }
        }

        .ant-modal-footer {
          .width-prop(80, height);

          .ant-btn-default {
            border: 1px solid rgba(255, 255, 255, 0.4) !important;
            background-color: transparent;
            color: rgba(255, 255, 255, 0.88);
            .font-size(14);
            .width-prop(32, height);
            .px2vw(12);
            padding: 0 @vw;

            &:not([disabled]) {
              &:hover {
                opacity: 0.88;
                background-color: transparent !important;
              }
            }
          }
        }
      }
    }
  }
</style>
