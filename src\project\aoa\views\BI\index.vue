<template>
  <div class="overview-page">
    <div class="page-decorate">
      <div class="page-decorate-left"> </div>
      <div class="page-decorate-right"> </div>
      <div class="page-decorate-bottom"> </div>
    </div>
    <div class="page-head">
      <ADateTime :theme="theme" />
      <div class="title" @click="back">精确曝气工艺看板</div>
      <div class="info">
        <AWeater :theme="theme" />
      </div>
    </div>
    <div class="page-content">
      <div class="content-inner">
        <div class="content-row content-row-left">
          <div
            class="row-item"
            :data-resource-code="indexCodeList[0].groupCode"
            style="height: 42.32%"
          >
            <!-- 未端溶解氧 -->
            <OxygenCard :data="oxygenData" :groupCode="indexCodeList[0].groupCode" />
          </div>
          <div
            class="row-item"
            :data-resource-code="nitrateData.value || indexCodeList[3].groupCode"
            style="height: 26.98%"
          >
            <!-- 硝氮数据 -->
            <NitrateCard
              :data="nitrateData"
              :groupCode="indexCodeList[3].groupCode"
              @update:value="handleChangeNitrateValue"
            />
          </div>
          <div
            class="row-item"
            :data-resource-code="indexCodeList[1].groupCode"
            style="height: 27.47%"
          >
            <!-- 氨氮数据 -->
            <AmmoniaCard :data="ammoniaData" :groupCode="indexCodeList[1].groupCode" />
          </div>
        </div>
        <div class="content-row content-row-right">
          <div
            class="row-item"
            :data-resource-code="indexCodeList[2].groupCode"
            style="height: 26.99%"
          >
            <!-- 进水数据 -->
            <WaterCard :data="waterData" />
          </div>
          <div class="row-item" style="height: 33.65%">
            <!-- 吨水电耗统计 -->
            <WaterCostCard :data="electricityData.data" :first="electricityData.first" />
          </div>
          <div class="row-item" style="height: 36.13%">
            <!-- 实时报警 -->
            <AlarmCard :data="alarmData" />
          </div>
        </div>
      </div>
      <div class="in-water" :data-resource-code="indexCodeList[5].groupCode">
        <!-- 进水流量 -->
        <BottomCard :data="waterFlowData" :groupCode="indexCodeList[5].groupCode" />
      </div>
    </div>
    <!-- <tipsItem
      v-for="(item, index) in tipsItemData"
      :key="index"
      :data="item.data"
      :style="formatTipsStyle(item)"
      :theme="theme"
    /> -->
    <template v-for="(item, index) in tipsItemSmallData" :key="index">
      <tipsItemSmall
        v-if="index > 0"
        :data="item.data"
        :style="`position: absolute; left: ${item.position?.[0]}; top: ${item.position?.[1]};z-index: 1;`"
        :theme="theme"
      />
    </template>
    <template v-for="(img, index) in tipsImgFirst" :key="index">
      <div :style="formatStyle(img)"> {{ img.name }}：{{ img.value }}{{ img.unit }} </div>
    </template>
    <template v-for="(img, index) in tipsImgLast" :key="index">
      <div :style="formatStyle(img)"> {{ img.name }}：{{ img.value }}{{ img.unit }}</div>
    </template>
    <bgVideo @imgSize="imgSize" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onUnmounted, CSSProperties } from 'vue';
  // import { useDomain } from '/@/locales/useDomain';
  // import { useUserStore } from '/@/store/modules/user';
  import dayjs from 'dayjs';
  import { useRouter } from 'vue-router';
  import { getParams } from '/@aoa/utils';
  import { ADateTime } from '/@aoa/components/ADateTime';
  import { AWeater } from '/@aoa/components/AWeater';
  import { indexCodeList } from './data';
  import { mockWaterFlowData, mockElectricityChartData } from './data';
  import { centerParams } from '../cockpit/data';
  import { callResourceFunction } from '/@/api/config-center/scenes-group';
  import { getSenceGroupTree } from '/@zhcz/api/scenes-group';
  import { getBiEvent } from '/@aoa/api/event-center';
  import { getFactoryId } from '/@aoa/utils/factory';
  import { useIntervalFn } from '@vueuse/core';
  import OxygenCard from './components/OxygenCard.vue';
  import AmmoniaCard from './components/AmmoniaCard.vue';
  import WaterCard from './components/WaterCard.vue';
  import NitrateCard from './components/NitrateCard.vue';
  import WaterCostCard from './components/WaterCostCard.vue';
  import AlarmCard from './components/AlarmCard.vue';
  import BottomCard from './components/BottomCard.vue';
  // import tipsItem from '../cockpit/components/tipsItem.vue';
  // import firstImg from '../cockpit/assets/images/1chi.png';
  // import secondImg from '../cockpit/assets/images/2chi.png';
  // import thirdImg from '../cockpit/assets/images/3chi.png';
  import hight_d_bg from '../cockpit/assets/images/hight_d_bg_2.png';
  import tipsItemSmall from '../cockpit/components/tipsItemSmall.vue';
  import bgVideo from '../cockpit/components/bgVideo.vue';

  // import { positionList } from './data';
  // import { codeList } from './data';

  import type { LineChartData, DataList, DataListSelectData, AlarmData } from './type';
  const theme = ref<'light' | 'dark'>('dark');
  const router = useRouter();
  const indexList = ref<any[]>([]);

  const innerWidth = window.innerWidth;
  const baseWidth = 1920;
  const baseHeight = 1080;
  const imgRect = ref({ left: 0, top: 0, width: baseWidth, height: baseHeight });
  const tipsItemSmallData = ref<any>([
    {
      data: {
        title: '生化池',
        data: [
          // {
          //   name: '日均耗电大概25,144kWh',
          //   // value: '0.8',
          //   // unit: 'mg/L',
          // },
          // {
          //   name: '溶解氧',
          //   value: '0.5',
          //   unit: 'mg/L',
          // },
        ],
        config: {
          theme: '',
        },
      },
      position:
        innerWidth < 1500
          ? ['49.54%', '43.2%']
          : innerWidth < 1926 && innerWidth > 1500
          ? ['43.54%', '36.2%']
          : innerWidth > 1926 && innerWidth < 2566
          ? ['49.54%', '45.2%']
          : ['49.54%', '43.2%'],
    },
    {
      data: {
        title: '鼓风机房',
        data: [
          {
            name: '日均耗电大概',
            value: '25,144',
            unit: 'kWh',
          },
          // {
          //   name: '溶解氧',
          //   value: '0.5',
          //   unit: 'mg/L',
          // },
        ],
        config: {
          theme: '',
        },
      },
      position:
        innerWidth < 1500
          ? ['63.756%', '10.648%']
          : innerWidth < 1926
          ? ['64.756%', '18.648%']
          : innerWidth > 1926
          ? ['60.756%', '15.648%']
          : ['60.756%', '17.648%'],
    },
  ]);
  const tipsImgFirst = ref<any>([
    {
      src: hight_d_bg,
      position: ['22.083', '42.222'], // 全屏 1080
      // position:
      //   innerWidth < 1500
      //     ? ['15.083', '45.722']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['25.083', '40.722']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['25.083', '41.722']
      //     : ['25.083', '43.722'],
      width: '153.85',
      height: '36.57',
      name: 'COD',
      value: '-',
      unit: 'mg/L',
      rotate: 330.8,
    },
    {
      src: hight_d_bg,
      position: ['30.1666', '34.577'],
      // position:
      //   innerWidth < 1500
      //     ? ['25.1666', '36.777']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['32.1666', '32.777']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['31.1666', '35.777']
      //     : ['32.1666', '35.777'],
      width: '153.85',
      height: '36.57',
      name: 'TN',
      value: '-',
      unit: 'mg/L',
      rotate: 330.8,
    },
    {
      src: hight_d_bg,
      position: ['39.858', '24.729'],
      // position:
      //   innerWidth < 1500
      //     ? ['36.458', '27.729']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['39.458', '24.729']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['39.458', '27.729']
      //     : ['39.458', '28.729'],
      width: '153.85',
      height: '36.57',
      name: '氨氮',
      value: '-',
      unit: 'mg/L',
      rotate: 330.8,
    },
    {
      src: hight_d_bg,
      position: ['47.629', '17.492'],
      // position:
      //   innerWidth < 1500
      //     ? ['46.229', '19.492']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['46.229', '17.492']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['46.229', '20.492']
      //     : ['46.229', '22.492'],
      width: '153.85',
      height: '36.57',
      name: 'TP',
      value: '-',
      unit: 'mg/L',
      rotate: 330.8,
    },
  ]);
  const tipsImgLast = ref<any>([
    {
      src: hight_d_bg,
      position: ['56.837', '72.169'],
      // position:
      //   innerWidth < 1500
      //     ? ['53.837', '76.169']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['56.837', '72.169']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['58.837', '74.169']
      //     : ['55.837', '76.169'],
      width: '153.85',
      height: '36.57',
      name: 'COD',
      value: '-',
      unit: 'mg/L',
      rotate: 322.29,
    },
    {
      src: hight_d_bg,
      position: ['64.234', '62.163'],
      // position:
      //   innerWidth < 1500
      //     ? ['62.234', '66.163']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['63.234', '62.163']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['65.234', '65.163']
      //     : ['62.234', '67.163'],
      width: '153.85',
      height: '36.57',
      name: '氨氮',
      value: '-',
      unit: 'mg/L',
      rotate: 322.29,
    },
    {
      src: hight_d_bg,
      position: ['71.074', '52.692'],
      // position:
      //   innerWidth < 1500
      //     ? ['70.974', '56.692']
      //     : innerWidth > 1500 && innerWidth < 1926
      //     ? ['68.974', '52.692']
      //     : innerWidth > 1926 && innerWidth < 2566
      //     ? ['72.974', '54.692']
      //     : ['68.974', '57.692'],
      width: '153.85',
      height: '36.57',
      name: 'PH',
      value: '-',
      unit: '',
      rotate: 324.29,
    },
  ]);
  const imgSize = (img: any) => {
    imgRect.value = { ...img };
  };
  const formatStyle = (img: any): CSSProperties => {
    const scale = imgRect.value.width / baseWidth;
    const left = imgRect.value.left + Number(img?.position[0]) * 0.01 * 1920 * scale + 'px';
    const top = imgRect.value.top + Number(img?.position[1]) * 0.01 * 1080 * scale + 'px';
    const width = Number(img.width) * scale + 'px';
    const height = Number(img.height) * scale + 'px';
    return {
      background: 'url(' + img.src + ') no-repeat',
      backgroundSize: '100% 100%',
      position: 'absolute',
      left,
      top,
      width,
      height,
      lineHeight: height,
      transform: `rotate(${img.rotate}deg)`,
      fontFamily: 'PingFang SC',
      fontWeight: 600,
      fontSize: 14 * scale + 'px',
      color: '#FFFFFF',
      textAlign: 'center',
      textShadow: '0px 1px 2px rgba(0, 0, 0, 0.88)',
      zIndex: 1,
    };
  };

  // 鼓风机房
  const getGFJFData = async () => {
    const params = getParams(centerParams[3]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const formatRes = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      tipsItemSmallData.value[1].data.data = formatRes;
    } else {
      tipsItemSmallData.value[1].data.data = [
        {
          name: '日均耗电大概',
          value: '25144',
          unit: 'kWh',
        },
        // {
        //   name: '溶解氧',
        //   value: '0.5',
        //   unit: 'mg/L',
        // },
      ];
    }
  };
  // 进水水质
  const getJSSZData = async () => {
    const params = getParams(centerParams[1]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const formatRes = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
        // name: 'COD',
        // value: '-',
        // unit: 'mg/L',
      });
      tipsImgLast.value = tipsImgLast.value.map((item, index) => {
        return { ...item, ...formatRes[index] };
      });
    } else {
      // tipsImgFirst.value = [
      //   {
      //     src: hight_d_bg,
      //     position: ['22.083%', '34.722%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'COD',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 330.8,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['29.1666%', '27.777%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'TN',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 330.8,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['36.458%', '19.629%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: '氨氮',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 330.8,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['43.229%', '12.592%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'TP',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 330.8,
      //   },
      // ];
    }
  };
  // 出水水质
  const getCSSZData = async () => {
    const params = getParams(centerParams[2]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const formatRes = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      tipsImgFirst.value = tipsImgFirst.value.map((item, index) => {
        return { ...item, ...formatRes[index] };
      });
    } else {
      // tipsImgLast.value = [
      //   {
      //     src: hight_d_bg,
      //     position: ['58.837%', '81.169%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'COD',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 324.29,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['65.234%', '72.163%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: '氨氮',
      //     value: '-',
      //     unit: 'mg/L',
      //     rotate: 324.29,
      //   },
      //   {
      //     src: hight_d_bg,
      //     position: ['71.974%', '62.692%'],
      //     width: '153.85px',
      //     height: '36.57px',
      //     name: 'PH',
      //     value: '-',
      //     unit: '',
      //     rotate: 324.29,
      //   },
      // ];
    }
  };

  /**  未端溶解氧 */
  const oxygenData = ref<DataList[]>([]);
  const getOxygenData = async () => {
    const params = getParams(indexCodeList[0]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      oxygenData.value = data;
    } else {
      oxygenData.value = [];
    }
  };

  /** 氨氮数据 */
  const ammoniaData = ref<DataList[]>([]);

  const getAmmoniaData = async () => {
    const params = getParams(indexCodeList[1]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      ammoniaData.value = data;
    } else {
      ammoniaData.value = [];
    }
  };

  /** 进水数据 */
  const waterData = ref<DataList[]>([
    {
      value: '0',
      name: '当月进水量',
      indexCode: '',
      digit: 0,
      unit: 'm³',
    },
    {
      value: '0',
      name: '同期进水量',
      indexCode: '',
      digit: 0,
      unit: 'm³',
    },
  ]);

  // 进水量
  const getWaterData = async () => {
    const todayDate = [
      dayjs().startOf('month').format('YYYY-MM-DD 00:00:00'),
      dayjs().format('YYYY-MM-DD 23:59:59'),
    ];
    const yesterdayDate = [
      dayjs().subtract(1, 'year').startOf('month').format('YYYY-MM-DD 00:00:00'),
      dayjs().subtract(1, 'year').format('YYYY-MM-DD 23:59:59'),
    ];
    const todayParams = getParams(indexCodeList[2], todayDate[0], todayDate[1]);
    const yesterdayParams = getParams(indexCodeList[2], yesterdayDate[0], yesterdayDate[1]);

    const [todayData, yesterdayData] = await Promise.all([
      callResourceFunction(todayParams),
      callResourceFunction(yesterdayParams),
    ]);
    if (todayData && todayData.length) {
      waterData.value[0].digit = todayData[0]?.digit ?? 0;
      waterData.value[0].indexCode = todayData[0]?.indexCode;
      waterData.value[0].value = todayData[0]?.value;
      waterData.value[0].unit = todayData[0]?.unitName;
    } else {
      waterData.value[0].digit = 0;
      waterData.value[0].indexCode = '';
      waterData.value[0].value = null;
      waterData.value[0].unit = '';
    }

    if (yesterdayData && yesterdayData.length) {
      waterData.value[1].digit = yesterdayData[0]?.digit ?? 0;
      waterData.value[1].indexCode = yesterdayData[0]?.indexCode;
      waterData.value[1].value = yesterdayData[0]?.value;
      waterData.value[1].unit = yesterdayData[0]?.unitName;
    } else {
      waterData.value[1].digit = 0;
      waterData.value[1].indexCode = '';
      waterData.value[1].value = null;
      waterData.value[1].unit = '';
    }
  };

  /** 硝氮数据 */
  const nitrateData = reactive<DataListSelectData>({
    value: '',
    options: [],
    data: [],
  });

  const setNitrateOptions = async () => {
    const data =
      indexList.value.find((item) => item.groupCode === indexCodeList[3].groupCode)?.children || [];
    nitrateData.options = data?.map((item) => ({
      value: item.groupCode,
      label: item.name,
    }));
    nitrateData.value = nitrateData.options.length ? nitrateData.options[0]?.value : '';
  };
  const getNitrateData = async () => {
    if (!nitrateData.value) return;
    const tempParams = { ...indexCodeList[3], groupCode: nitrateData.value };
    const params = getParams(tempParams);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      nitrateData.data = data;
    } else {
      nitrateData.data = [];
    }
  };

  const handleChangeNitrateValue = (value) => {
    nitrateData.value = value;
    getNitrateData();
  };

  /** 吨水单耗 */
  const electricityData = reactive<LineChartData>({
    data: mockElectricityChartData,
    first: true,
  });

  const getElectricityData = async () => {
    electricityData.first = false;
    const startDateTime = dayjs().startOf('month').format('YYYY-MM-DD 00:00:00');
    const endDateTime = dayjs().endOf('month').format('YYYY-MM-DD 23:59:59');
    const params = getParams(indexCodeList[4], startDateTime, endDateTime);

    // const res = await callResourceFunction(params);
    // 传统电耗
    // const _startDateTime = dayjs().startOf('month').format('2024-MM-DD 00:00:00');
    // const _endDateTime = dayjs().endOf('month').format('2024-MM-DD 23:59:59');
    // const _params = getParams(indexCodeList[9], _startDateTime, _endDateTime);

    const [res] = await Promise.all([callResourceFunction(params)]);
    if (res && res.length) {
      electricityData.data = res.map((item) => {
        return {
          name: item.indexName,
          // name1: _res.length ? _res[index].indexName : '',
          value: item.value,
          // value1: _res.length ? _res[index].value : null,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
          collectDateTime: item.collectDateTime,
        };
      });
    } else {
      electricityData.data = mockElectricityChartData;
    }
    electricityData.first = true;
  };

  const levelMap = {
    一级报警: 1,
    二级报警: 2,
    三级报警: 3,
  };
  /** 实时报警 */
  const alarmData = ref<AlarmData[]>([]);
  const getAlarmData = async () => {
    const params = {
      factoryId: getFactoryId(),
    };
    const res = await getBiEvent(params);
    alarmData.value = res.map((item) => ({
      id: item.id,
      title: item.topic,
      warnValue: item.xianzhi,
      limitValue: item.fankuizhi,
      warnEventLevel: levelMap[item.level],
      creationTime: item.time,
    }));
  };

  /** 中心流量 */
  const waterFlowData = ref<DataList[]>(mockWaterFlowData);

  const getWaterFlowData = async () => {
    const params = getParams(indexCodeList[5]);
    const res = await callResourceFunction(params);
    if (res && res.length) {
      const data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
      waterFlowData.value = data;
    } else {
      waterFlowData.value = mockWaterFlowData;
    }
  };

  // 硝氮数据
  const getNitrateDataAsync = async () => {
    await setNitrateOptions();
    await getNitrateData();
  };

  const getData = () => {
    getOxygenData();
    getAmmoniaData();
    getWaterData();
    getWaterFlowData();
    getElectricityData();
    getAlarmData();
    getGFJFData();
    getJSSZData();
    getCSSZData();
    // getHaoYangData();
    // getQYangData();
    // getYYangData();
  };

  const getPollData = () => {
    getOxygenData();
    getAmmoniaData();
    getWaterData();
    getGFJFData();
    getJSSZData();
    getCSSZData();
    getNitrateData();
    getWaterFlowData();
    getElectricityData();
    getAlarmData();
    // getHaoYangData();
    // getQYangData();
    // getYYangData();
  };

  const getResourceData = async () => {
    const data = await getSenceGroupTree();
    // 工艺监控
    indexList.value =
      data
        .find((item) => item.senceCode === 'JQBQ_V3')
        ?.children?.find((item) => item.groupCode === 'JQBQ_GYJK')?.children || [];
    if (indexList.value.length) {
      getNitrateDataAsync();
    }
  };

  getResourceData();
  getData();

  const { pause } = useIntervalFn(getPollData, 10 * 1000);
  onUnmounted(() => {
    pause();
  });

  // const { getTenantId, getDomain } = useDomain();
  // const userStore = useUserStore();
  // const factoryId = computed(() => userStore.getCurrentFactoryId);
  // 返回
  const back = () => {
    const path = router.currentRoute.value.meta?.backRoutePath ?? '';
    if (path) {
      // router.push({
      //   path: path as string,
      //   query: {
      //     tenantId: getTenantId.value,
      //     domain: getDomain.value,
      //     factoryId: factoryId.value,
      //   },
      // });
      router.push(path);
    } else {
      router.go(-1);
    }
  };
</script>

<style lang="less" scoped>
  @import '/@aoa/assets/css/font.less';

  .overview-page {
    position: relative;
    width: 100%;
    height: 100%;
    // background: url('./assets/images/bi_bim.png') center center no-repeat;
    // background-size: cover;
    overflow: hidden;
    color: #fff;

    .page-decorate {
      .page-decorate-left,
      .page-decorate-right {
        position: absolute;
        width: 20px;
        top: 20px;
        height: calc(100% - 52px);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center top;
        overflow: hidden;
        z-index: 99;
      }

      .page-decorate-left {
        left: 0;
        background-image: url('./assets/images/bi_left_bg.png');
      }

      .page-decorate-right {
        right: 0;
        background-image: url('./assets/images/bi_right_bg.png');
      }

      .page-decorate-bottom {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        height: 18px;
        background-size: 100% 100%;
        background-image: url('./assets/images/bi_footer.png');
        z-index: 99;
      }
    }

    .page-head {
      position: relative;
      padding: 14px 32px 0;
      width: 100%;
      height: 72px;
      background: url('./assets/images/title.png') center top no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 10;

      .title {
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
        font-family: 'Alimama ShuHeiTi';
        letter-spacing: 4px;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 60px;
        cursor: pointer;
      }

      .info {
        display: flex;
        width: max-content;
      }
    }

    .page-content {
      position: relative;
      padding: 10px 28px 6px;
      width: 100%;
      height: calc(100% - 72px - 18px);

      .content-inner {
        display: flex;
        justify-content: space-between;
        gap: 12px;
        height: 100%;

        .content-row {
          height: 100%;
          display: flex;
          justify-content: space-between;
          gap: 12px;

          .row-item {
            position: relative;
          }

          &-left,
          &-right {
            width: 292px;
            display: flex;
            flex-direction: column;
            z-index: 2;
          }

          &-middle {
            position: relative;
            flex: 1;
            padding: 0 12px;
            z-index: 2;
          }
        }
      }
    }
  }

  @media screen and (min-width: 1800px) {
    .overview-page {
      .page-content {
        .content-inner {
          .content-row {
            &-left,
            &-right {
              .width-prop(372);
            }
          }
        }
      }
    }
  }

  @media screen and (min-width: 2000px) {
    .overview-page {
      .page-decorate {
        .page-decorate-left,
        .page-decorate-right {
          .width-prop(24);
          .px2vh(64);
          height: calc(100% - @vh);
          .height-prop(24, top);
        }

        .page-decorate-bottom {
          .height-prop(24, height);
        }
      }

      .page-head {
        .px2vw(32);
        .px2vh(14);
        padding: @vh @vw 0;
        .height-prop(94, height);

        .title {
          .font-size(28);
          .height-prop(86, line-height);
        }
      }

      .page-content {
        .px2vw(28);
        .px2vh(12);
        padding: @vh @vw;
        height: calc(100% - (94 / @design-height) * 100vh - (24 / @design-height) * 100vh);
      }
    }
  }
</style>
