<template>
  <div class="production-control-kit">
    <a-form
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      :colon="false"
      labelAlign="left"
      autocomplete="off"
    >
      <template v-for="(formItem, formIndex) in formList" :key="formIndex">
        <a-form-item label="套件名称" v-show="formItem.data.frameType == 1">
          <a-input v-model:value="formItem.data.kitDisplayName" />
        </a-form-item>
        <a-form-item label="名称字号" v-show="formItem.data.frameType == 1">
          <a-input-number
            style="width: 100%"
            v-model:value="formItem.data.businessData.kitConfig.title.fontSize"
          />
        </a-form-item>
        <a-form-item label="名称颜色" v-show="formItem.data.frameType == 1">
          <color-picker
            v-model:value="formItem.data.businessData.kitConfig.title.color"
            :input="true"
            :setDefault="false"
          />
        </a-form-item>
        <a-form-item label="是否显示">
          <a-radio-group
            v-model:value="formItem.data.displayMode"
            :options="displayOptions"
            @change="onDisplayChange"
          />
        </a-form-item>
        <a-row>
          <a-col :span="24">
            <a-form-item label="外框类型">
              <a-select v-model:value="formItem.data.frameType" @change="onFrameTypeChange">
                <a-select-option
                  v-for="(item, borderIndex) in borderOptions"
                  :key="borderIndex"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="formItem.data.frameType === 1">
            <a-form-item label="边框图片">
              <UploadCardBg
                :url="formItem.data.frameBackground"
                :frameBackgroundType="formItem.data.frameBackgroundType"
                @background-type-change="handleBackgroundTypeChange"
                @background-url-change="handleBackgroundUrlChange"
              />
            </a-form-item>
          </a-col>
          <!-- && formItem.data.frameBackgroundType === frameBackgroundTypeEnum.CUSTOM -->
          <a-col :span="24" v-show="formItem.data.frameType === 1">
            <a-form-item label="边框内边距">
              <a-row :gutter="[4, 8]">
                <a-col :span="10">
                  <a-input-number
                    placeholder="上"
                    :min="0"
                    :precision="0"
                    v-model:value="formItem.data.framePadding[0]"
                  />
                </a-col>
                <a-col :span="10">
                  <a-input-number
                    placeholder="右"
                    :min="0"
                    :precision="0"
                    v-model:value="formItem.data.framePadding[1]"
                  />
                </a-col>
                <a-col :span="10">
                  <a-input-number
                    placeholder="下"
                    :min="0"
                    :precision="0"
                    v-model:value="formItem.data.framePadding[2]"
                  />
                </a-col>
                <a-col :span="10">
                  <a-input-number
                    placeholder="左"
                    :min="0"
                    :precision="0"
                    v-model:value="formItem.data.framePadding[3]"
                  />
                </a-col>
              </a-row>
            </a-form-item>
          </a-col>

          <a-col :span="24" v-show="typeof formItem.data.frameType === 'number'">
            <a-form-item label="外框方位">
              <a-select v-model:value="formItem.data.framePlacement" @change="onPlacementChange">
                <a-select-option
                  v-for="(item, placementIndex) in placementOptions"
                  :key="placementIndex"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="24">
            <a-form-item label="行高">
              <a-input-number
                style="width: 100%"
                v-model:value="formItem.data.businessData.kitConfig.column.height"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="文字颜色">
              <color-picker
                v-model:value="formItem.data.businessData.kitConfig.column.color"
                :input="true"
                :setDefault="false"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="第一列">
              <a-button style="width: 100%" @click="handleOpen(0)">配置</a-button>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="第二列">
              <a-button style="width: 100%" @click="handleOpen(1)">配置</a-button>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="第三列">
              <a-button style="width: 100%" @click="handleOpen(2)">配置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
        <a-col :span="24">
          <a-form-item label="禁止指标弹窗">
            <a-switch v-model:checked="formItem.data.businessData.isShowIndicator" />
          </a-form-item>
        </a-col>
        <a-row>
          <a-col :span="24">
            <a-form-item label="指标接口">
              <a-select v-model:value="formItem.data.businessData.resourceInterfaceId">
                <a-select-option
                  v-for="(item, interfaceIndex) in interfaceOptions"
                  :key="interfaceIndex"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col style="padding-top: 4px" :span="8">显示内容</a-col>
          <a-col :span="16">
            <TreeSelect
              v-model:value="formItem.data.businessData.tree"
              style="width: 100%"
              :tree-data="treeData"
              tree-checkable
              allow-clear
              :show-checked-strategy="SHOW_PARENT"
              placeholder="请选择数据集"
              show-search
              tree-node-filter-prop="label"
              @change="onTreeSelectChange"
            />
          </a-col>
        </a-row>
        <a-row class="expressions">
          <a-col :span="24">
            <a-collapse
              class="collapseRef"
              ghost
              :bordered="false"
              v-model:activeKey="activeKey"
              expand-icon-position="right"
            >
              <a-collapse-panel
                v-for="(item, expressionIndex) in formItem.data.businessData
                  .displayProductDataInfos"
                :key="getExpressionKey(item, expressionIndex)"
                :header="item.displayName || '表达式'"
              >
                <template #extra>
                  <img
                    :src="AddBorderUrl"
                    style="width: 20px; margin-right: 16px"
                    @click.stop="addExpression(expressionIndex)"
                  />
                  <img
                    :src="DelBorderUrl"
                    class="w-[20px]"
                    @click.stop="delExpression(expressionIndex)"
                  />
                </template>

                <!-- Code选择框 -->
                <a-form-item label="Code选择">
                  <a-select
                    v-model:value="item.code"
                    style="width: 100%"
                    placeholder="请选择Code"
                    :filter-option="filterCodeOption"
                    @change="(code) => onCodeSelectChange(code, expressionIndex)"
                  >
                    <a-select-option
                      v-for="codeOption in availableCodeOptions"
                      :key="codeOption.value"
                      :value="codeOption.value"
                    >
                      {{ codeOption.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="指标值">
                  <a-input
                    style="width: 100%"
                    v-model:value="item.expression"
                    @blur="() => handleExpressionChange(expressionIndex, item)"
                  />
                </a-form-item>
                <a-form-item label="单位">
                  <a-input v-model:value="item.unit" />
                </a-form-item>
                <a-col :span="24">
                  <a-form-item label="显示名称">
                    <a-input v-model:value="item.displayName" />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="显示别名">
                    <a-input v-model:value="item.shortDisplayName" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="启用别名">
                    <a-switch v-model:checked="item.isShowShort" />
                  </a-form-item>
                </a-col>

                <!-- 选择类型：数值、文本、 开关-->
                <a-col :span="24">
                  <a-form-item label="类型">
                    <a-select v-model:value="item.indicatorType">
                      <a-select-option
                        v-for="expressionType in indicatorTypeOptions"
                        :key="expressionType.value"
                        :value="expressionType.value"
                      >
                        {{ expressionType.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>

                <template v-if="item.indicatorType === INDICATOR_TYPE_ENUM.NUMBER">
                  <a-form-item label="是否显示诊断">
                    <a-switch v-model:checked="item.isWarning" />
                  </a-form-item>
                  <a-form-item label="表达式类型">
                    <a-select
                      v-model:value="item.scriptType"
                      :allowClear="true"
                      @change="handleScriptTypeChange($event, expressionIndex, item)"
                    >
                      <a-select-option
                        v-for="expressionType in expressionTypeOptions"
                        :key="expressionType.value"
                        :value="expressionType.value"
                      >
                        {{ expressionType.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item
                    label="报警规则"
                    v-show="
                      item.scriptType == expressionTypeEnum.rule &&
                      item.scriptType !== expressionTypeEnum.regular
                    "
                  >
                    <a-select
                      v-model:value="item.warningRuleId"
                      :indicatorReq="getCurrentWarnRuleList(expressionIndex, item)"
                      :allowClear="true"
                    >
                      <a-select-option
                        v-for="warnRule in indicatorCodeOptions[expressionIndex].value"
                        :key="warnRule.value"
                        :value="warnRule.value"
                      >
                        {{ warnRule.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item
                    label="计算表达式"
                    v-show="item.scriptType && item.scriptType !== expressionTypeEnum.regular"
                  >
                    <a-button style="width: 100%" @click="openEditorModal(item, expressionIndex)">
                      ...
                    </a-button>
                  </a-form-item>

                  <a-row
                    v-if="item.isNumber === true && item.scriptType === expressionTypeEnum.regular"
                  >
                    <!-- 数值型 -->
                    <a-row style="width: 100%">
                      <a-col :span="8">
                        <div style="line-height: 32px">上限值</div>
                      </a-col>
                      <a-col :span="10">
                        <a-form-item :wrapper-col="{ span: 24 }">
                          <a-input-number style="width: 100%" v-model:value="item.upperLimits" />
                        </a-form-item>
                      </a-col>
                      <a-col :span="6">
                        <a-form-item :wrapper-col="{ span: 24 }">
                          <div style="text-align: right">
                            <a-checkbox v-model:checked="item.isUpperEqual"> 可等 </a-checkbox>
                          </div>
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row style="width: 100%">
                      <a-col :span="8">
                        <div style="line-height: 32px">下限值</div>
                      </a-col>
                      <a-col :span="10">
                        <a-form-item :wrapper-col="{ span: 24 }">
                          <a-input-number style="width: 100%" v-model:value="item.lowerLimits" />
                        </a-form-item>
                      </a-col>
                      <a-col :span="6">
                        <a-form-item :wrapper-col="{ span: 24 }">
                          <div style="text-align: right">
                            <a-checkbox v-model:checked="item.isLowerEqual"> 可等 </a-checkbox>
                          </div>
                        </a-form-item>
                      </a-col>
                    </a-row>

                    <a-col :span="24">
                      <a-form-item label="常规颜色">
                        <color-picker
                          v-model:value="item.normalColor"
                          :input="true"
                          :setDefault="false"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="超上限颜色">
                        <color-picker
                          v-model:value="item.greaterThanUpperColor"
                          :input="true"
                          :setDefault="false"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="超下限颜色">
                        <color-picker
                          v-model:value="item.lowerThanLowerColor"
                          :input="true"
                          :setDefault="false"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <template v-if="item.IsNumber === false">
                    <!-- 文字型 -->
                    <a-row
                      :gutter="16"
                      v-for="(valueItem, valueIndex) in item.values"
                      :key="valueIndex"
                    >
                      <a-col :span="24" style="padding-bottom: 8px">
                        <div class="value-wrapper">
                          <span>预设值</span>
                          <div class="action">
                            <img
                              v-show="valueIndex === 0"
                              :src="AddBorderUrl"
                              style="width: 20px"
                              @click="addValue(index)"
                            />
                            <img
                              v-show="valueIndex !== 0"
                              :src="DelBorderUrl"
                              style="width: 20px"
                              @click="delValue(index, valueIndex)"
                            />
                          </div>
                        </div>
                      </a-col>
                      <a-col :span="18">
                        <a-form-item :wrapper-col="{ span: 24 }">
                          <a-input v-model:value="valueItem.value" />
                        </a-form-item>
                      </a-col>
                      <a-col :span="6">
                        <a-form-item :wrapper-col="{ span: 24 }">
                          <color-picker
                            v-model:value="valueItem.color"
                            :input="true"
                            :setDefault="false"
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </template>
                </template>
                <template v-if="item.indicatorType === INDICATOR_TYPE_ENUM.TEXT">
                  <a-form-item label="文本表达式">
                    <a-button style="width: 100%" @click="openEditorModal(item, expressionIndex)">
                      ...
                    </a-button>
                  </a-form-item>
                </template>
                <template v-if="item.indicatorType === INDICATOR_TYPE_ENUM.SWITCH">
                  <a-form-item label="开关表达式">
                    <a-button style="width: 100%" @click="openEditorModal(item, expressionIndex)">
                      ...
                    </a-button>
                  </a-form-item>

                  <a-form-item label="URL">
                    <a-input v-model:value="item.controlUrl" disabled />
                  </a-form-item>
                </template>
              </a-collapse-panel>
            </a-collapse>
          </a-col>
        </a-row>
      </template>
      <ColConfigModal @register="register" @update-col-config="handleUpdateColConfig" />
    </a-form>
    <BasicModal v-model:open="visible" title="Javascript" :width="800" :bodyStyle="{ padding: 0 }">
      <div class="text-right pb-3">
        <a-button type="second" @click="createTemplate"> 生成模版 </a-button>
      </div>
      <div style="height: 400px">
        <json-editor v-if="visible" v-model="scriptStr" ref="editorRef" />
      </div>
      <template #footer>
        <a-button @click="handleCancel"> 取消 </a-button>
        <a-button type="primary" @click="handleOk"> 确定 </a-button>
      </template>
    </BasicModal>
  </div>
</template>

<script lang="ts">
  import {
    Form,
    FormItem,
    InputNumber,
    Select,
    SelectOption,
    RadioGroup,
    Row,
    Col,
    Collapse,
    CollapsePanel,
    Switch,
    Checkbox,
    TreeSelect,
  } from 'ant-design-vue';
  import { ColorPicker } from '/@process-editor/components/ColorPicker';
  import ColConfigModal from './ColConfigModal.vue';

  import JsonEditor from '../JsonEditor.vue';
  import { BasicModal } from '/@/components/Modal';
  import {
    displayOptions,
    initExpression,
    initValue,
    borderOptions,
    placementOptions,
    // defaultDisplayProductControlDataInfo,
    expressionTypeOptions,
    expressionTypeEnum,
  } from './kit.data';

  const AForm = Form;
  const AFormItem = FormItem;
  const AInputNumber = InputNumber;
  const ASelect = Select;
  const ASelectOption = SelectOption;
  const ARadioGroup = RadioGroup;
  const ARow = Row;
  const ACol = Col;
  const ACollapse = Collapse;
  const ACollapsePanel = CollapsePanel;
  const ASwitch = Switch;
  const ACheckbox = Checkbox;

  export {};
</script>

<script setup lang="ts">
  import { ref, onMounted, reactive, watch } from 'vue';
  import { cloneDeep } from 'lodash-es';
  import SortTable from 'sortablejs';
  import {
    getSenceGroupIndicatorTreeApi,
    getResourceInterfacePage,
    getWarnEventPage,
  } from '/@process-editor/api/index';
  import { updatePropData } from './utils';
  import { useModal } from '/@/components/Modal';
  import { updatePositionNoBorder, updatePositionHasBorder } from '/@process-editor/core/kits/data';
  import { treeToList } from '/@process-editor/views/editor/utils/index';
  import UploadCardBg from './components/UploadCardBg.vue';
  import { indicatorTypeOptions, INDICATOR_TYPE_ENUM } from '/@process-editor/constant';
  import AddBorderUrl from '/@process-editor/assets/images/add-border.png';
  import DelBorderUrl from '/@process-editor/assets/images/del-border.png';
  import { getScriptTemplateByIndicatorItem } from '/@process-editor/utils';
  import useSelectCode from './hooks/useSelectCode.ts';
  import { getAddExpressionData } from './utils';

  const props = defineProps({
    formList: {
      type: Array,
      default: () => [
        {
          data: {},
        },
      ],
    },
    data: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
    activePen: {
      type: Object,
      default: () => {},
    },
  });
  const UPDATE_DATA = 'update:data';
  const emits = defineEmits(['change-view-mode', 'update:data']);

  const activeKey = ref(['expression1']);

  const SHOW_PARENT = TreeSelect.SHOW_PARENT;
  const treeData = ref([]);

  const [register, { openModal }] = useModal();

  const { updateAvailableCodeOptions, onCodeSelectChange, availableCodeOptions } = useSelectCode(
    props,
    treeData,
    updateFormDataByProp,
  );

  // 过滤code选项
  function filterCodeOption(input, option) {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }

  function handleOpen(index) {
    openModal(true, {
      data: props.data.businessData.kitConfig.column,
      index,
    });
  }

  function handleUpdateColConfig(data) {
    const index = data.index;
    const form = data.form;
    updateFormDataByProp((data) => {
      data.businessData.kitConfig.column.width[index] = form.width;
      data.businessData.kitConfig.column.fontSize[index] = form.fontSize;
      data.businessData.kitConfig.column.padding[index] = {
        paddingTop: form.paddingTop,
        paddingRight: form.paddingRight,
        paddingBottom: form.paddingBottom,
        paddingLeft: form.paddingLeft,
      };
      data.businessData.kitConfig.column.textAlign[index] = form.textAlign;
      data.businessData.kitConfig.column.show[index] = form.show;
      data.businessData.kitConfig.column.isShowUnit[index] = form.isShowUnit;
    });
  }

  onMounted(() => {
    if (typeof meta2d !== 'undefined') {
      meta2d.on('editPen', () => {
        onPlacementChange(props.data.framePlacement);
      });
    }
    initSortTable();
    // 初始化可用的code选项
    updateAvailableCodeOptions();
  });

  function onTreeSelectChange(ids) {
    function handleData(ids) {
      const nodeList = treeToList(treeData.value);
      const nodes = nodeList.filter((i) => ids.includes(i.value));
      const _nodes_ = nodes.map((i) => i.children || i).flat(Infinity);
      const indicators = _nodes_
        .map((i) =>
          i.indicators
            ? i.indicators.map((j) => ({
                ...j,
                groupCode: i.groupCode,
                resourceInterfaceId: i.defaultInterfaceId,
              }))
            : [],
        )
        .flat();

      return function (data) {
        const rawData = indicators.map((i) => {
          return getAddExpressionData(i);
        });

        data.businessData.displayProductDataInfos = rawData;
        data.businessData.tree = ids;
      };
    }

    updateFormDataByProp(handleData(ids));

    // 更新可用的code选项
    updateAvailableCodeOptions();
  }

  const interfaceOptions = ref([]);
  async function getInterfaceList() {
    // current=1&size=10&resourceIndexId=1&factoryId=2024&orgId=waterBusiness&appId=station&renterId=2024
    const params = {
      current: 1,
      pageSize: 999,
      resourceIndexId: 1,
    };
    const data = await getResourceInterfacePage(params);
    interfaceOptions.value = (data.records || []).map((i) => ({
      label: i.interfaceName,
      value: i.id,
    }));
  }
  getInterfaceList();

  async function getTreeData() {
    const data = await getSenceGroupIndicatorTreeApi();
    treeData.value = data;
    // 初始化可用的code选项
    updateAvailableCodeOptions();
  }
  getTreeData();

  function initSortTable() {
    const el = document.getElementsByClassName('collapseRef')[0];
    const options = {
      onEnd(evt) {
        updateFormDataByProp((data) => {
          const rawData = cloneDeep(props.data.businessData.displayProductDataInfos);
          data.businessData.displayProductDataInfos = swapElements(
            rawData,
            evt.oldIndex,
            evt.newIndex,
          );
        });
      },
    };
    const sortTable = new SortTable(el, options);

    return sortTable;
  }

  /**
   * 交换数组中两个元素的位置
   * @param {Array} data 数组
   * @param {Number} index1 旧下标
   * @param {Number} index2 新下标
   * @returns {Array} 新数组
   */
  function swapElements(data, index1, index2) {
    if (data.length < 2) return data;

    const array = cloneDeep(data);
    [array[index1], array[index2]] = [array[index2], array[index1]];

    return array;
  }

  function updateFormDataByProp(cb) {
    const params = [props.data, emits, UPDATE_DATA];
    updatePropData(params, cb);
  }

  function onDisplayChange() {
    emits('change-view-mode');
  }

  function onFrameTypeChange(frameType) {}

  function handleBackgroundTypeChange(frameBackgroundType) {
    updateFormDataByProp((data) => {
      data.frameBackgroundType = frameBackgroundType;
    });
  }

  function handleBackgroundUrlChange(frameBackground) {
    updateFormDataByProp((data) => {
      data.frameBackground = frameBackground;
    });
  }

  function getExpressionKey(item, index) {
    return `${item.code}expression${index + 1}`;
  }

  function addExpression(index) {
    updateFormDataByProp((data) => {
      let addItem = cloneDeep(initExpression);
      data.businessData.displayProductDataInfos.splice(index + 1, 0, addItem);
      const length = data.businessData.displayProductDataInfos.length;
      activeKey.value = [];
    });
  }

  function delExpression(index) {
    updateFormDataByProp((data) => {
      data.businessData.displayProductDataInfos.splice(index, 1);
      const length = data.businessData.displayProductDataInfos.length;
      activeKey.value = [`expression${length}`];
    });

    // 更新可用的code选项
    updateAvailableCodeOptions();
  }

  function addValue(index) {
    updateFormDataByProp((data) => {
      data.expressions[index].values.push(cloneDeep(initValue));
    });
  }

  function delValue(index, valueIndex) {
    updateFormDataByProp((data) => {
      data.expressions[index].values.splice(valueIndex, 1);
    });
  }

  function noBorderPlacement(e) {
    updateFormDataByProp((data) => {
      updatePositionNoBorder(e, data, props.activePen);
    });
  }

  function borderPlacement(e) {
    updateFormDataByProp((data) => {
      updatePositionHasBorder(e, data, props.activePen);
    });
  }

  function onPlacementChange(e) {
    const type = props.data.frameType;
    switch (type) {
      case 0:
        noBorderPlacement(e);
        break;
      case 1:
        borderPlacement(e);
        break;
      default:
        break;
    }
  }

  const editorRef = ref(null);

  const visible = ref(false);
  const scriptStr = ref('');
  const lambdaIndex = ref(-1);
  const expressionItem = ref(null);

  function openEditorModal(record, index) {
    scriptStr.value = record.script;
    console.log('-----------------> lambdaIndex', index);
    lambdaIndex.value = index;
    expressionItem.value = record;
    visible.value = true;
  }

  function handleOk() {
    visible.value = false;
    updateFormDataByProp((data) => {
      if (lambdaIndex.value > -1) {
        console.log('-----------------> scriptStr', scriptStr.value);
        data.businessData.displayProductDataInfos[lambdaIndex.value].script = scriptStr.value;
      }
    });
  }

  function handleCancel() {
    visible.value = false;
    scriptStr.value = '';
    lambdaIndex.value = -1;
    expressionItem.value = null;
  }

  function handleScriptTypeChange(scriptType, index, record) {
    updateFormDataByProp((data) => {
      data.businessData.displayProductDataInfos[index].script = '';
      data.businessData.displayProductDataInfos[index].warningRuleId = '';
    });
  }

  function createTemplate() {
    const editor = editorRef.value;
    let scriptTemplate = '';
    if (!expressionItem.value) return scriptTemplate;

    scriptTemplate = getScriptTemplateByIndicatorItem(expressionItem.value);

    editor.setValue(scriptTemplate);
    editor.formatDocument();
  }

  // getWarnRuleList();
  let indicatorCodeOptions = reactive(
    new Array(999).fill('').map((i) => ({ value: [], isInit: false })),
  );
  async function getCurrentWarnRuleList(index, item, isFresh = false) {
    let indicatorCode = item.code;
    // indicatorCode = extractStringBetweenQuotes(indicatorCode);
    if (!indicatorCode) return;
    const params = {
      current: 1,
      pageSize: 999,
      indicatorCode,
    };
    if (!isFresh && indicatorCodeOptions[index].isInit) return;
    indicatorCodeOptions[index].isInit = true;
    const data = await getWarnEventPage(params);
    indicatorCodeOptions[index].value = (data.records || []).map((i) => ({
      label: i.title,
      value: i.id,
    }));
  }

  // 指标值变动，重新请求报警规则
  function handleExpressionChange(index, item) {
    // getCurrentWarnRuleList(index, item.expression, true);
  }

  watch(
    () => props.data,
    (newVal) => {
      updateAvailableCodeOptions();
    },
  );
</script>

<style lang="less" scoped>
  .ant-row.expressions,
  .ant-row.frame {
    margin-top: 20px;
    background-color: #f8f8f8;

    // .ant-form-item:last-child {
    //   margin-bottom: 0;
    // }

    img {
      cursor: pointer;
    }
  }

  .ant-row.expressions {
    .value-wrapper {
      display: flex;
      justify-content: space-between;
    }

    ::v-deep(.el-color-picker) {
      width: 100%;

      .el-color-picker__trigger {
        width: 100%;
      }
    }
  }

  .ant-row.frame {
    padding: 12px 16px;
  }
</style>

<style lang="less">
  .tag-item {
    .text {
      font-size: 12px;
      flex-shrink: 0;
      line-height: 22px;
      cursor: pointer;
      color: @primary-color;
    }
  }
</style>
