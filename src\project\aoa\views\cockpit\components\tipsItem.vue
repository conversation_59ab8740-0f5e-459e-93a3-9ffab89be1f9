<template>
  <div
    :class="[
      'tipsITem-component',
      data.data && data.data.length ? setClassName(data.data.length) : '',
    ]"
    ref="rootRef"
    :theme="data.config.theme"
    @click="handleClick"
    :style="{
      backgroundImage: classSet ? `url(${tipsITem_BG_2[indexc]})` : `url(${tipsITem_BG[indexc]})`,
    }"
  >
    <div class="content">
      <div :class="['content-item-title', { set_child_title: classSet }]">{{ data.title }}</div>
      <div class="content-item-content">
        <div
          :class="{ 'content-item': true, set_child: classSet }"
          :style="`
            background: ${classSet ? 'none' : `url(${child_bg[1]})`} ;
            background-size: 100% 100%;
            background-repeat: no-repeat;`"
          v-for="(item, index) in data.data"
          :key="item.name + index"
        >
          <div class="content-item-name">
            <!-- <img class="child_img" :src="water[index % 2]" alt="" srcset="" /> -->
            <span :title="item.name">{{ item.name }}</span>
          </div>
          <div class="content-item-value">
            <span class="content-item-value-num">{{ item.value }}</span>
            <span class="content-item-value-unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick } from 'vue';
  // import tipsITemWater0 from '../assets/images/tipsITemWater0.png';
  // import tipsITemWater1 from '../assets/images/tipsITemWater1.png';
  import tipsITem_child_bg0 from '../assets/images/tipsITem_child_bg0.png';
  import tipsITem_child_bg1 from '../assets/images/tipsITem_child_bg1.png';
  import tipsITem_bg_1 from '../assets/images/tipsITem_bg_1.png';
  import tipsITem_bg_1_2 from '../assets/images/tipsITem_bg_1_2.png';
  import tipsITem_bg_1_3 from '../assets/images/tipsITem_bg_1_3.png';
  import tipsITem_bg_2 from '../assets/images/tipsITem_bg_2.png';
  import tipsITem_bg_2_2 from '../assets/images/tipsITem_bg_2_2.png';
  import tipsITem_bg_2_3 from '../assets/images/tipsITem_bg_2_3.png';
  const child_bg = [tipsITem_child_bg0, tipsITem_child_bg1];
  const tipsITem_BG = [tipsITem_bg_1, tipsITem_bg_1_2, tipsITem_bg_1_3];
  const tipsITem_BG_2 = [tipsITem_bg_2, tipsITem_bg_2_2, tipsITem_bg_2_3];
  // const water = [tipsITemWater0, tipsITemWater1];

  defineProps({
    data: {
      type: Object as any,
      default: () => ({}),
      //   {
      //   title: '好洋区',
      //   data: [
      //     {
      //       name: '氨氮',
      //       value: '0.8',
      //       unit: 'mg/L',
      //     },
      //     {
      //       name: '溶解氧',
      //       value: '0.5',
      //       unit: 'mg/L',
      //     },
      //   ],
      //   config: {
      //     theme: '',
      //   },
      // }
    },
    classSet: {
      type: Boolean,
      default: () => false,
    },
    indexc: {
      type: Number,
      default: 0,
    },
  });
  const setClassName = (val) => {
    return `tipsItem${val}`;
  };
  const handleClick = () => {
    // emit('click');
  };
  const rootRef = ref<HTMLElement | null>(null);
  const emit = defineEmits(['sizeChange']);
  const updateSize = () => {
    if (rootRef.value) {
      const rect = rootRef.value.getBoundingClientRect();
      console.log('rect.width rect.height', rect.width, rect.height);
      emit('sizeChange', { width: rect.width, height: rect.height });
    }
  };
  onMounted(() => {
    nextTick(updateSize);
  });
</script>
<style lang="less" scoped>
  .tipsITem-component {
    width: 154px;
    min-height: 100px;
    background-size: 100% 100%;
    padding-top: 8px;
    padding-left: 8px;
    // padding-bottom: 8px;

    position: relative;

    // &.class_bg2 {
    //   background-image: url('../assets/images/tipsITem_bg_1.png');
    // }

    // &.class_bg {
    //   background-image: url('../assets/images/tipsITem_bg_2.png');
    // }

    &.tipsItem1 {
      padding-bottom: 0rem;
    }

    &.tipsItem2 {
      padding-bottom: 1.1rem;
    }

    &.tipsItem3 {
      padding-bottom: 1.25rem;
    }

    .content {
      display: flex;
      flex-direction: column;

      .content-item-title {
        border-radius: 4px;
        background: linear-gradient(
          180deg,
          rgba(1, 137, 112, 0.256) 0%,
          rgba(1, 138, 113, 0.64) 100%
        );
        box-shadow: inset 2px 2px 2px 0px #a2ffef, inset -2px -2px 2px 0px #032119;
        margin-right: 8px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 15px;
        color: #ffffff;
        text-shadow: 2px 1px 1px #0a382e;
        margin-bottom: 6px;

        &.set_child_title {
          background: rgba(6, 202, 166, 0.4);
          box-shadow: none;
        }
      }

      .content-item-content {
        // max-height: 4.25rem;
        overflow-y: auto;
        border-radius: 8px;
        // margin-bottom: 0.25rem;
      }

      .content-item {
        width: 138px;
        height: 32px;
        display: flex;
        padding: 8px;
        padding-right: 4px;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;

        &.set_child {
          background: rgba(1, 36, 61, 0.56) !important;
          border: 1px solid rgba(255, 255, 255, 0.16);
        }

        &:last-child {
          margin-bottom: 0px;
        }
        // background-image: url('../BI/assets/images/tipsITem_block.png');
        // background-size: 130px 101px;

        .content-item-value {
          display: flex;
          align-items: center;

          .content-item-value-num {
            font-family: PingFang SC;
            font-weight: 700;
            font-size: 16px;
            color: #ffffff;
            line-height: 16px;
          }

          .content-item-value-unit {
            display: inline-block;
            width: 38.67px;
            text-align: left;
            padding-left: 4px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #ebebeb;
            line-height: 14px;
          }
        }

        .content-item-name {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 16px;
          display: flex;
          align-items: center;
          // text-overflow: ellipsis;
          // overflow: hidden;
          white-space: nowrap;
          margin-right: 8px;

          .child_img {
            margin-right: 9px;
            width: 28px;
            height: 28px;
          }
        }
      }
    }
  }
</style>
