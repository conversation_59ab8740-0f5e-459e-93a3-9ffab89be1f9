/* stylelint-disable value-list-comma-space-after */
@import './pagination.less';
@import './popover.less';
@import './switch.less';
@import './radio.less';
@import './tooltip.less';
@import './tabs.less';
@import './input.less';
@import './btn.less';
@import './collapse.less';
@import './table.less';
@import './picker.less';
@import './modal.less';
@import './tree.less';
@import './select.less';
@import './drawer.less';
@import './message.less';
@import './tag.less';
@import './select-tree.less';
@import './alert.less';
@import './dropdown.less';

.ant-image-preview-root {
  img {
    display: unset;
  }
}

span.anticon:not(.app-iconify) {
  font-size: 16px;
  // margin-right: 6px;
  vertical-align: -0.2em !important;
}

.ant-back-top {
  right: 20px;
  bottom: 20px;
}

.collapse-container__body {
  > .ant-descriptions {
    margin-left: 6px;
  }
}

.ant-image-preview-operations {
  background-color: rgb(0 0 0 / 30%);
}

.ant-popover {
  &-content {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }
}

// =================================
// ==============modal message======
// =================================
.modal-icon-warning {
  color: @warning-color !important;
}

.modal-icon-success {
  color: @success-color !important;
}

.modal-icon-error {
  color: @error-color !important;
}

.modal-icon-info {
  color: @primary-color !important;
}

// =================================
// ============== message======
// =================================

.hlxb-message {
  &-notice-content {
    max-width: 50%;
    padding: 0 !important;
    background: transparent !important;

    .hlxb-message-loading {
      .anticon {
        color: @theme-color;
      }
    }
  }

  &-custom-content {
    padding: 9px 12px;
    border-radius: 4px;
    border: 1px solid transparent;
  }

  &-success {
    border-color: @message-success-aoa3;
    background: @message-success-aoa3-16p;
  }

  &-error {
    border-color: @message-error-aoa3;
    background: @message-error-aoa3-16p;
  }

  &-warning {
    border-color: @message-warning-aoa3;
    background: @message-warning-aoa3-16p;
  }

  &-loading {
    border-color: #fff;
    background: #fff;
  }
}

.ant-checkbox-checked .ant-checkbox-inner::after,
.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
  border-top: 0 !important;
  border-left: 0 !important;
}

.ant-form-item-control-input-content {
  > div {
    > div {
      max-width: 100%;
    }
  }
}

.ant-slider {
  margin: 11px;
}

.ant-select-tree {
  &-switcher {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.ant-tree-select-dropdown {
  &.aoa3-tree {
    .ant-select-tree {
      background-color: @aoa3-join-from-bg;
    }

    .ant-select-tree-node-content-wrapper:hover {
      background-color: @theme-color-12p !important;
    }
  }
}
