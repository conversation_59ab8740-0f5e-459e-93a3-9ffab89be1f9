<template>
  <div :class="getMenuClass" ref="target">
    <div
      :class="['menu-action-box', { 'mouse-hover': visible }]"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <div class="menu-action">
        <slot>
          <Icon class="menu-action-icon" icon="icon-park-solid:system" />
          <span>{{ currentSystem }}</span>
        </slot>
      </div>

      <div class="menu-box">
        <div class="menu-list">
          <div
            :class="['menu-item', { active: currentIndex == item.meta!.moduleId }]"
            v-for="(item, index) in menuList"
            :key="index"
            @click="clickItemHandle(item)"
          >
            <img :src="item?.quickNavigationIcon" class="menu-icon" />
            <span class="menu-name" v-if="item.name.length <= 8">{{ item.name }}</span>
            <Tooltip v-else>
              <template #title>{{ item.name }}</template>
              <span class="menu-name">{{ item.name }}</span>
            </Tooltip>
            <!-- <img
              :src="moreSystemActionIcon"
              v-if="currentIndex == item.meta!.moduleId"
              class="menu-item-icon"
            /> -->
            <div class="select" v-if="currentIndex == item.meta!.moduleId">
              <Checkbox disabled v-model:checked="checked" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="MoreSystemMenu">
  import { ref, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { Tooltip, Checkbox } from 'ant-design-vue';
  import { propTypes } from '/@/utils/propTypes';
  import { useTabs } from '/@/hooks/web/useTabs';
  // import moreSystemActionIcon from '/@/assets/images/menu/system-menu-active-icon.png';
  const { prefixCls } = useDesign('more-system-menu');
  import { isUrl } from '/@/utils/is';

  const props = defineProps({
    theme: propTypes.oneOf(['dark', 'light']),
  });

  const checked = ref(true);
  const visible = ref(false);
  const target = ref<HTMLElement | null>(null);

  const { refreshMenu, permissionStore } = usePermission();
  const tabs = useTabs();

  const menuList = computed(() => permissionStore.getFirstMenuList);

  const currentIndex = computed(() => {
    return permissionStore.getFirstMenuParams?.moduleId;
  });

  const currentSystem = computed(() => {
    return permissionStore.getFirstMenuParams?.title;
  });

  const getMenuClass = computed(() => [prefixCls, `${prefixCls}-${props.theme}`]);

  function handleMouseEnter() {
    visible.value = true;
  }

  function handleMouseLeave() {
    visible.value = false;
  }
  async function clickItemHandle(item, toHome = true) {
    const {
      redirect,
      meta: { moduleId, title },
      path,
      quickNavigationIcon,
    } = item;
    if (currentIndex.value == moduleId) return;
    if (isUrl(path)) {
      window.open(path);
      return;
    }
    visible.value = false;
    permissionStore.setFirstMenuParams({
      moduleId,
      redirect,
      title,
      icon: quickNavigationIcon,
    });

    await permissionStore.setBackMenuList(item.children || []);
    await permissionStore.patchHomeAffix();
    await refreshMenu(toHome, true);
    tabs.refreshPage();
  }
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-more-system-menu';
  .@{prefix-cls} {
    position: relative;
    margin: 0 16px;

    &:before {
      position: absolute;
      content: '';
      width: 1px;
      height: 24px;
      left: -16px;
      top: 50%;
      transform: translateY(-50%);
      background: @theme-color-32p;
    }

    .menu-action-box {
      position: relative;
      padding: 5px 0;

      &.mouse-hover {
        .menu-action {
          background: rgba(255, 255, 255, 0.32);
        }

        .menu-box {
          display: block !important;
          animation: fadeIn 0.4s forwards;
        }
      }
    }

    .menu-action {
      height: 32px;
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 16px;
      color: #fff;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 4px 4px 4px 4px;
      gap: 0 8px;

      .menu-action-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .menu-title {
        line-height: 16px;
      }
    }

    &-light {
      &.@{prefix-cls} {
        .menu-action-box {
          &.mouse-hover {
            .menu-action {
              // background: @theme-color-20p;
              background: rgba(255, 255, 255, 0.16);
            }
          }
        }

        .menu-action {
          // color: @theme-color;
          // background-color: @theme-color-12p;
          // color: rgba(2, 62, 51, 1);
          color: #fff;
          background-color: transparent;

          .menu-action-icon {
            filter: brightness(0) contrast(100%);
          }
        }
      }
    }

    .menu-box {
      display: none;
      position: absolute;
      top: 36px;
      left: 0;
      opacity: 0;
    }

    .menu-list {
      max-width: 440px;
      width: max-content;
      display: flex;
      flex-wrap: wrap;
      padding: 20px 20px 0;
      gap: 0 20px;
      background: @aoa3-join-from-bg;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 24%);
      border-radius: 8px;
      transition: 0.4s;
      position: relative;

      &.ani {
        animation: fadeIn 0.5s ease-in forwards;
      }
    }

    .menu-item {
      width: 120px;
      height: 94px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(17, 144, 120, 0.12);
      border-radius: 8px;
      position: relative;
      // margin-right: 20px;
      margin-bottom: 20px;
      cursor: pointer;
      padding: 0 4px;
      text-align: center;
      overflow: hidden;

      &:nth-child(3n + 3) {
        margin-right: 0;
      }

      &:hover {
        // background: @theme-color-12p;
        background: rgba(17, 144, 120, 0.2);
        outline: 1px solid @theme-color;
      }

      &.active {
        // background: @theme-color-12p;
        background: rgba(17, 144, 120, 0.2);
        outline: 1px solid @theme-color;
      }

      .menu-item-icon {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 0;
        bottom: 0;
      }

      .select {
        position: absolute;
        right: -19px;
        bottom: -4px;
        width: 40px;
        height: 40px;
        background: var(--theme-color-8p);
        border: 1px solid var(--theme-color);
        border: none;
        background-color: transparent;
        border-radius: 50%;

        :deep(.ant-checkbox-wrapper) {
          width: 100%;
          height: 100%;

          &-disabled {
            cursor: pointer;
          }

          .ant-checkbox {
            width: 100%;
            height: 100%;

            &-disabled {
              .ant-checkbox-inner {
                &:after {
                  border-color: #fff;
                }
              }
            }

            .ant-checkbox-inner {
              top: -1px;
              width: 100%;
              height: 100%;
              background-color: transparent;
              border: none;

              &:after {
                width: 6px;
                height: 11px;
                top: 11px;
                left: 8px;
                border-radius: 1px;
                transform: rotate(42deg) scale(1) translate(-50%, -50%);
              }
            }

            &.ant-checkbox-checked {
              background: var(--theme-color);
              border-radius: 50%;

              &:after {
                border: none !important;
              }
            }
          }
        }
      }

      .menu-icon {
        width: 34px;
        height: 34px;
        margin-bottom: 10px;
      }

      .menu-name {
        width: 100%;
        font-size: 14px;
        font-weight: 600;
        color: #333;
        line-height: 16px;
        white-space: nowrap; /* 让文本不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 使用省略号代替超出部分 */
      }
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
      top: 36px;
    }

    10% {
      opacity: 1;
    }

    100% {
      top: 42px;
      opacity: 1;
    }
  }
</style>
