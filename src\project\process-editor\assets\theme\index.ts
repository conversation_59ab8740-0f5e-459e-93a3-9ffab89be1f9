export const aoaThemeColor = {
  '--hlxb-business-ui-text-color': '#333',
  '--hlxb-business-ui-disabled-text-color': '#999',
  '--hlxb-business-ui-border-color': '#C5DBD3',
  '--hlxb-business-ui-background-color': '#E1F3F1FF',
  '--hlxb-business-ui-primary-color': '#119078ff',
  '--hlxb-business-ui-dropdown-color': '#e1f3f1ff',
  '--hlxb-business-ui-hover-bg-color': '#11907829',
  '--hlxb-business-ui-dropdown-active-bg-color': '#119078ff',
  '--hlxb-business-ui-picker-start-end-bg-color': '#119078ff',
  '--hlxb-business-ui-picker-in-view-bg-color': '#becfc9',
  '--hlxb-business-ui-scrollbar-thumb-color': '#7abcb5',
  '--hlxb-business-ui-scrollbar-track-color': '#bfdcd9',
  '--hlxb-business-ui-scrollbar-corner-color': '#bfdcd9',
  '--hlxb-business-ui-scrollbar-track-hover-color': '#529c94',
  '--hlxb-business-ui-modal-close-color': '#666',
  '--hlxb-business-ui-btn-default-bg-color': '#fff',
  '--hlxb-business-ui-btn-default-hover-bg-color': '#f8f8f8',
  '--hlxb-business-ui-active-bg-text-color': '#fff',
  '--hlxb-business-ui-icon-color': '#666',
  '--hlxb-business-ui-table-head-bg-color': 'rgba(24,184,139,.2)',
  '--hlxb-business-ui-table-cell-bg-color': 'unset',
  '--hlxb-business-ui-table-head-border-bottom': '1px solid #59b29b',
  '--hlxb-business-ui-table-cell-border-color': '#c1dbd2',
  '--hlxb-business-ui-input-bg': '#fff',
};
export const aoaEchartsConfig = {
  legend: {
    textStyle: { color: '#999999' },
    pageIconColor: '#333',
    pageIconInactiveColor: '#999',
    pageTextStyle: {
      color: '#999',
      fontSize: 14,
    },
  },
  xAxis: {
    axisLine: { lineStyle: { color: '#59B29B' } },
    axisLabel: { color: '#333' },
    axisPointer: {
      show: true,
      type: 'line',
      lineStyle: {
        color: '#119078',
        type: [5, 10],
      },
    },
  },
  yAxis: {
    nameTextStyle: { color: '#999999' },
    axisLine: { color: '#A1D0C4' },
    axisLabel: { color: '#333' },
    splitLine: { lineStyle: { color: '#A1D0C4' } },
  },
  tooltip: {
    backgroundColor: '#E1F3F1',
    borderColor: '#02695E',
    textStyle: {
      textStyle: 'bold',
    },
  },
};

export function getProcessEditorTheme() {
  return { themeColor: {}, echartsConfig: {} };
}
