<template>
  <BasicModal
    :canFullscreen="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    title="绑定资源"
    width="1200px"
    :minHeight="688"
    :wrapClassName="['data-configuration-modal', { aoa3: isTabsLabel }]"
    :loading="okLoading"
    destroyOnClose
    :useWrapper="false"
  >
    <Input v-model:value="inputVal" placeholder="请输入" allowClear @change="handleSearchStr">
      <template #prefix>
        <img :src="searchIcon" style="width: 16px" />
      </template>
    </Input>
    <div :class="['content-container', { aoa3: isTabsLabel }]">
      <div class="type-container">
        <div
          v-for="typeItem in dataTypes"
          :key="typeItem.id"
          :class="{ active: selectedType === typeItem.value }"
          @click="handleSelectType(typeItem.value)"
        >
          {{ typeItem.label }}
        </div>
      </div>
      <div>
        <div class="indicator-container">
          <div>
            <Select
              v-model:value="selectedSource"
              style="width: 200px"
              placeholder="请选择"
              allowClear
              :options="dataSources"
              @change="handleSelectSource"
            />
            <Select
              v-model:value="selectedTimeType"
              style="width: 200px"
              placeholder="请选择"
              class="ml-4"
              allowClear
              :options="timeTypes"
              @change="handleSelectTimeType"
            />
          </div>
          <BasicTable @register="registerTable" />
        </div>
      </div>
    </div>
    <div :class="['checked-container', { aoa3: isTabsLabel }]">
      <div class="checked-header-container">
        <div>序号</div>
        <div>资源标识</div>
        <div>展示名称</div>
        <div>资源单位</div>
        <!-- <div>标签</div> -->
        <div>开启上下限</div>
        <div>上限</div>
        <div>下限</div>
        <div>小数点位数</div>
        <div>操作</div>
      </div>
      <Draggable
        group="form-draggable"
        class="checked-scroll-container"
        tag="div"
        :component-data="{
          tag: 'div',
          type: 'transition-group',
          name: 'list',
        }"
        ghostClass="moving"
        :animation="180"
        v-model="draggableList"
        handle=".th-drag"
        item-key="key"
        ref="draggableListRef"
        @scroll="draggableListScroll"
      >
        <template #item="{ element: item, index }">
          <div class="checked-item">
            <div class="th-drag"> <Icon icon="carbon:drag-vertical" /> {{ index + 1 }}</div>
            <div>
              <!-- <Tooltip>
                <template #title> {{ item.sourceUniqueKey }}</template>
                {{ item.sourceUniqueKey }}
              </Tooltip> -->
              <EllipsisTooltip :text="item.sourceUniqueKey" />
            </div>

            <div class="flex items-center">
              <Input
                v-show="item.isEditName"
                :ref="
                  (el) => {
                    setInputRef(el, 'name', index);
                  }
                "
                v-model:value="item.displayName"
                @blur="item.isEditName = false"
              />
              <div
                v-show="!item.isEditName"
                class="edit-item"
                @click="handleShowEdit(index, 'name')"
              >
                <!-- <Tooltip>
                  <template #title> {{ item.displayName }}</template>
                  {{ item.displayName || '-' }}
                </Tooltip> -->
                <EllipsisTooltip :text="item.displayName" />
              </div>
            </div>

            <div class="flex items-center">
              <Input
                v-show="item.isEditUnit"
                :ref="
                  (el) => {
                    setInputRef(el, 'unit', index);
                  }
                "
                v-model:value="item.unitName"
                @blur="item.isEditUnit = false"
              />
              <div
                v-show="!item.isEditUnit"
                class="edit-item"
                @click="handleShowEdit(index, 'unit')"
              >
                <span>{{ item.unitName || '-' }}</span>
              </div>
            </div>

            <!-- <div class="flex items-center">
              <Input
                v-show="item.isEditTag"
                :ref="
                  (el) => {
                    setInputRef(el, 'tag', index);
                  }
                "
                v-model:value="item.tag"
                @blur="item.isEditTag = false"
              />
              <div v-show="!item.isEditTag" class="edit-item" @click="handleShowEdit(index, 'tag')">
                <Tooltip>
                  <template #title> {{ item.tag || '-' }}</template>
                  {{ item.tag || '-' }}
                </Tooltip>
              </div>
            </div> -->

            <div class="flex items-center">
              <Checkbox v-model:checked="item.openLimit" />
            </div>

            <div class="flex items-center">
              <Input
                v-show="item.isEditMaxVal"
                :ref="
                  (el) => {
                    setInputRef(el, 'maxVal', index);
                  }
                "
                v-model:value="item.maxVal"
                @blur="item.isEditMaxVal = false"
              />
              <div
                v-show="!item.isEditMaxVal"
                class="edit-item"
                @click="handleShowEdit(index, 'maxVal')"
              >
                <span>{{ item.maxVal || '-' }}</span>
              </div>
            </div>

            <div class="flex items-center">
              <Input
                v-show="item.isEditMinVal"
                :ref="
                  (el) => {
                    setInputRef(el, 'minVal', index);
                  }
                "
                v-model:value="item.minVal"
                @blur="item.isEditMinVal = false"
              />
              <div
                v-show="!item.isEditMinVal"
                class="edit-item"
                @click="handleShowEdit(index, 'minVal')"
              >
                <span>{{ item.minVal || '-' }}</span>
              </div>
            </div>

            <div class="flex items-center">
              <InputNumber
                v-show="item.isEditDigitVal"
                :ref="
                  (el) => {
                    setInputRef(el, 'digitVal', index);
                  }
                "
                v-model:value="item.digit"
                :min="0"
                :precision="0"
                :controls="false"
                @blur="item.isEditDigitVal = false"
              />
              <div
                v-show="!item.isEditDigitVal"
                class="edit-item"
                @click="handleShowEdit(index, 'digitVal')"
              >
                <span>{{ item.digit === null || item.digit === undefined ? 2 : item.digit }}</span>
              </div>
            </div>

            <div class="delete-btn" @click="handleDeleteItem(item)">删除</div>
          </div>
        </template>
      </Draggable>
    </div>
    <div :class="['bottom-container', { aoa3: isTabsLabel }]">
      <div class="tips">
        <span>已选择</span>
        <span style="color: var(--theme-table-text-active-color)">{{ draggableList.length }}</span>
        <span>个资源</span>
        <span style="color: var(--theme-table-text-active-color)">(展示名称和展示单位可编辑)</span>
      </div>
      <span class="clear-btn" @click="clearList">清空</span>
    </div>
    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">保存</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup name="BindModal">
  import Draggable from 'vuedraggable';
  import { Icon } from '/@/components/Icon';
  import { ref, nextTick, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { EllipsisTooltip } from '/@/components/EllipsisTooltip';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Input, Checkbox, InputNumber, Select } from 'ant-design-vue';
  import {
    addOrUpdateDisplayResourceInfos,
    getResourceInfos,
    deleteDisplayResourceInfos,
  } from '/@zhcz/api/config-center/scenes-group';
  import searchIcon from '/@/assets/images/search-icon.png';
  import { ResourceInfo } from '../typing';
  import { getIndicatorPageTable } from '/@zhcz/api/config-center/monitoring-points';
  import { BasicTable, useTable } from '/@/components/Table';
  import { bindIndicatorColumns } from '../data';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { DICT } from '/@/enums/dict';
  import { cloneDeep } from 'lodash-es';
  import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';

  const okLoading = ref(false);
  const inputVal = ref('');
  const indicatorList: any = ref([]);
  const checkedList = ref<ResourceInfo[]>([]);
  const nameInputRef = ref<HTMLElement[]>([]);
  const unitInputRef = ref<HTMLElement[]>([]);
  const tagInputRef = ref<HTMLElement[]>([]);
  const maxValInputRef = ref<HTMLElement[]>([]);
  const minValInputRef = ref<HTMLElement[]>([]);
  const digitValInputRef = ref<HTMLElement[]>([]);
  const draggableList = ref<ResourceInfo[]>([]);
  const draggableListRef = ref<HTMLElement>();
  const setInputRef = (el: any, type: string, index: number) => {
    if (!el) return;
    const inputRef = {
      name: nameInputRef,
      unit: unitInputRef,
      tag: tagInputRef,
      maxVal: maxValInputRef,
      minVal: minValInputRef,
      digitVal: digitValInputRef,
    };
    inputRef[type].value[index] = el;
  };
  const selectNode: any = ref({});
  const resourceIndexInfo: any = ref();
  // 初始已选资源
  const initCheckedList = ref<ResourceInfo[]>([]);
  const dataTypes: any = ref([]);
  const dataSources: any = ref([]);
  const timeTypes: any = ref([]);
  const selectedType = ref<string | number>(-1);
  const selectedSource = ref<string | null>(null);
  const selectedTimeType = ref<string | null>(null);

  const { getCardTabsType } = useMultipleTabSetting();
  const { getIsLabelType } = useMenuSetting();

  const isTabsLabel = computed(() => getIsLabelType && getCardTabsType);

  const [registerTable, { reload, setSelectedRowKeys, clearSelectedRowKeys }] = useTable({
    columns: bindIndicatorColumns,
    rowSelection: {
      type: 'checkbox',
      preserveSelectedRowKeys: true,
      onChange: (selectedRowKeys, selectedRows) => {
        const result: any = [];
        selectedRowKeys.forEach((key) => {
          const oldItem = initCheckedList.value.find((i) => i.sourceUniqueKey === key);
          if (oldItem) {
            result.push(oldItem);
          } else {
            const newItem = selectedRows.find((i) => i.indicatorCode === key);
            newItem.sourceUniqueKey = newItem.indicatorCode;
            newItem.displayName = newItem.indicatorName;
            newItem.unitName = newItem.unit;
            result.push(newItem);
          }
        });
        draggableList.value = result;
      },
    },
    clickToRowSelect: false,
    api: getIndicatorPageTable,
    beforeFetch: (pageParams) => {
      return {
        ...pageParams,
        dataType: selectedType.value === -1 ? null : selectedType.value,
        sourceType: selectedSource.value,
        timeType: selectedTimeType.value,
        indicatorOrName: inputVal.value,
      };
    },
    fetchSetting: {
      pageField: 'page',
      sizeField: 'size',
      listField: 'records',
      totalField: 'total',
    },
    showIndexColumn: false,
    useSearchForm: false,
    maxHeight: 200,
    rowKey: 'indicatorCode',
    inset: true,
  });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    selectNode.value = data.selectNode;
    resourceIndexInfo.value = data.resourceIndexInfo;
    initForm();
  });

  function handleSearchStr() {
    selectedType.value = -1;
    selectedSource.value = null;
    selectedTimeType.value = null;
    reload();
  }

  function handleSelectTimeType(value: string) {
    selectedTimeType.value = value;
    reload();
  }

  function handleSelectSource(value: string) {
    selectedSource.value = value;
    reload();
  }

  function handleSelectType(value: string) {
    if (selectedType.value === value) return;
    selectedType.value = value;
    reload();
  }

  const handleShowEdit = (index: number, type: string) => {
    const propertyMap = {
      name: 'isEditName',
      unit: 'isEditUnit',
      tag: 'isEditTag',
      maxVal: 'isEditMaxVal',
      minVal: 'isEditMinVal',
      digitVal: 'isEditDigitVal',
    };

    const property = propertyMap[type];
    if (property) {
      draggableList.value[index][property] = true;

      nextTick(() => {
        const inputRef = {
          name: nameInputRef,
          unit: unitInputRef,
          tag: tagInputRef,
          maxVal: maxValInputRef,
          minVal: minValInputRef,
          digitVal: digitValInputRef,
        }[type];

        if (inputRef) {
          inputRef.value[index].focus();
        }
      });
    }
  };

  const index = ref(0);
  const draggableListScroll = (event) => {
    const scrollHeight = event.target.scrollHeight;
    const scrollTop = event.target.scrollTop;
    if (scrollHeight - scrollTop <= 400) {
      index.value += 100;
      draggableList.value.push(...checkedList.value.slice(index.value, index.value + 100));
    }
  };

  const handleDeleteItem = (item: any) => {
    const result = draggableList.value.filter((i) => i.sourceUniqueKey !== item.sourceUniqueKey);
    setSelectedRowKeys(result.map((item: any) => item.sourceUniqueKey));
    draggableList.value = result;
  };

  const clearList = () => {
    clearSelectedRowKeys();
    draggableList.value = [];
  };

  // 初始化表单数据
  const initForm = async () => {
    okLoading.value = true;
    try {
      getDictTypeListApi({ type: DICT.MONITORING_POINT_DATA_TYPE }).then((data) => {
        dataTypes.value = data;
        dataTypes.value.unshift({ label: '全部', value: -1 });
      });
      getDictTypeListApi({ type: DICT.MONITORING_POINT_DATA_SOURCE }).then((data) => {
        dataSources.value = data.filter((item) => item.value !== '-1');
      });
      getDictTypeListApi({ type: DICT.MONITORING_POINT_DATA_TIME_TYPE }).then((data) => {
        timeTypes.value = data;
      });
      // 已选资源
      const checkedData = await getResourceInfos({
        resourceId: resourceIndexInfo.value.value,
        groupId: selectNode.value.id,
        sort: 1,
      });
      const sameTypeData = checkedData.filter(
        (item) => item.resourceType === resourceIndexInfo.value.intValue,
      );
      indicatorList.value.forEach((item: any) => {
        const findItem = sameTypeData.find((i: any) => i.sourceUniqueKey === item.sourceUniqueKey);
        item.checked = !!findItem;
        item.id = findItem?.id;
      });
      initCheckedList.value = cloneDeep(sameTypeData);
      setSelectedRowKeys(sameTypeData.map((item: any) => item.sourceUniqueKey));
      okLoading.value = false;
    } catch (error) {
      okLoading.value = false;
    }
  };

  async function handleSubmit() {
    try {
      okLoading.value = true;

      const deleteList = initCheckedList.value.filter(
        (item) => !draggableList.value.some((i) => i.sourceUniqueKey === item.sourceUniqueKey),
      );

      if (deleteList.length > 0) {
        await deleteDisplayResourceInfos({ ids: deleteList.map((item) => item.id) });
      }

      // 构建最终的资源列表
      const resultArr: any = draggableList.value.map((item) => {
        const existingItem = initCheckedList.value.find(
          (i) => i.sourceUniqueKey === item.sourceUniqueKey,
        );

        return (
          existingItem || {
            ...item,
            id: null,
          }
        );
      });

      // 构建提交数据
      const submitData = {
        resourceId: resourceIndexInfo.value.id,
        resourceType: resourceIndexInfo.value.intValue,
        groupId: selectNode.value.id,
        displayResources: resultArr.map((item, index) => ({
          id: item.id || null,
          sourceUniqueKey: item.sourceUniqueKey,
          displayName: item.displayName,
          unitName: item.unitName || '',
          tag: item.tag || '',
          maxVal: item.maxVal || '',
          minVal: item.minVal || '',
          openLimit: Number(item.openLimit),
          digit: item.digit,
          sourceUniqueKeyType: item.sourceUniqueKeyType,
          sort: index,
        })),
      };

      // 提交数据
      await addOrUpdateDisplayResourceInfos(submitData);

      // 提示成功并处理后续逻辑
      createMessage.success('操作成功');
      handleCancel();
      emit('success');
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    inputVal.value = '';
    initCheckedList.value = [];
    nameInputRef.value = [];
    unitInputRef.value = [];
    selectedType.value = -1;
    selectedSource.value = null;
    selectedTimeType.value = null;
  }
</script>

<style lang="less" scoped>
  .content-container {
    display: flex;
    border: 1px solid #e9e9e9;
    height: 350px;
    color: #333;
    margin: 12px 0;

    &.aoa3 {
      border: 1px solid @aoa3-join-from-border;

      .type-container {
        & > div {
          &:hover {
            background: @aoa3-table-row-hover-bg;
          }
        }
      }

      :deep(.vben-basic-table-aoa3) {
        .ant-table-wrapper {
          border-bottom: none;
          border-left: none;
          border-right: none;
          padding: 6px 0 16px;
          margin: 0 0 16px;
        }
      }
    }

    .type-container {
      width: 200px;
      flex-shrink: 0;
      overflow-y: scroll;

      & > div {
        cursor: pointer;
        height: 40px;
        line-height: 40px;
        padding: 0 12px;

        &:hover {
          background: #f0f0f0;
        }
      }

      .active {
        background: #f0f0f0;
        background-color: var(--theme-color-12p) !important;
        color: var(--theme-color);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          width: 4px;
          height: 100%;
          background-color: var(--theme-color);
          right: 0;
          top: 0;
        }
      }
    }

    & > div {
      padding: 12px;

      &:last-child {
        border-left: 1px solid #e9e9e9;
        position: relative;
      }
    }

    .select-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;
      font-weight: 500;
      color: #333;

      & > div {
        flex-shrink: 0;

        &:nth-child(1) {
          width: 180px;
        }

        &:nth-child(2) {
          width: 340px;
        }

        &:nth-child(3) {
          width: 80px;
        }
      }

      .checkAll-box {
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          margin-right: 8px;
        }

        img {
          cursor: pointer;
        }
      }
    }

    .indicator-container {
      .scroll-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        & > * {
          flex-shrink: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & > div {
          &:nth-child(1) {
            width: 180px;
          }

          &:nth-child(2) {
            width: 320px;
          }

          &:nth-child(3) {
            width: 80px;
          }
        }

        .ant-checkbox-wrapper {
          width: 52px;
          justify-content: flex-end;
        }
      }
    }
  }

  .checked-container {
    height: calc(36px + 190px + 32px + 12px);

    &.aoa3 {
      .checked-header-container {
        background-color: @aoa3-table-header-bg;
      }

      .checked-scroll-container {
        border: 1px solid @aoa3-join-from-border;
      }
    }
  }

  .checked-header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    line-height: 1;
    color: #333;
    height: 36px;
    background-color: #f1f4f7;
    padding: 0 16px;

    & > div {
      &:nth-child(1) {
        width: 50px;
      }

      &:nth-child(2) {
        width: 150px;
      }

      &:nth-child(3) {
        width: 140px;
      }

      &:nth-child(4) {
        width: 65px;
      }

      &:nth-child(5) {
        width: 80px;
      }

      &:nth-child(6) {
        width: 80px;
      }

      &:nth-child(7) {
        width: 80px;
      }

      &:nth-child(8) {
        width: 80px;
      }

      &:nth-child(9) {
        width: 80px;
      }

      &:nth-child(10) {
        width: 40px;
      }
    }
  }

  .checked-scroll-container {
    width: 100%;
    height: 190px;
    overflow-y: auto;
    border: 1px solid #e9e9e9;
    border-top: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .checked-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      padding: 0 16px;
      width: 100%;

      & > * {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      & > div {
        &:nth-child(1) {
          width: 50px;
        }

        &:nth-child(2) {
          width: 150px;
        }

        &:nth-child(3) {
          width: 140px;
        }

        &:nth-child(4) {
          width: 65px;
        }

        &:nth-child(5) {
          width: 80px;
        }

        &:nth-child(6) {
          width: 80px;
        }

        &:nth-child(7) {
          width: 80px;
        }

        &:nth-child(8) {
          width: 80px;
        }

        &:nth-child(9) {
          width: 80px;
        }

        &:nth-child(10) {
          width: 40px;
        }
      }

      :deep(.ant-input-number) {
        min-width: 100%;
      }

      .edit-item {
        cursor: text;
        position: relative;
        width: 100%;
        display: flex;
        align-items: center;

        .name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
          display: inline-block;
          line-height: 1;
          flex: 1;
        }

        :deep(span) {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
          display: inline-block;
          line-height: 1;
          flex: 1;
        }

        .icon {
          display: none;
        }

        &:hover {
          color: @aoa3-join-active-text;

          .icon {
            display: block;
          }
        }
      }

      .delete-btn {
        cursor: pointer;
        color: @aoa3-join-active-text;
      }
    }
  }

  .bottom-container {
    position: absolute;
    bottom: 0;
    height: 32px;
    border-top: 1px solid #e9e9e9;
    border-bottom: 1px solid #e9e9e9;
    left: 0;
    width: 100%;
    line-height: 32px;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;

    &.aoa3 {
      border-top: 1px solid @aoa3-join-from-border;
      border-bottom: 1px solid @aoa3-join-from-border;
    }

    .tips {
      color: #999999;
    }

    .clear-btn {
      color: @aoa3-join-active-text;
      cursor: pointer;
    }
  }
</style>

<style lang="less">
  .data-configuration-modal {
    .ant-input-affix-wrapper {
      border-radius: 2px;

      .ant-input-prefix img {
        width: 14px;
      }
    }

    .ant-select-selector {
      border-radius: 2px !important;
    }

    .ant-checkbox-inner {
      border-radius: 2px;
    }

    .ant-select-disabled {
      .ant-select-selector {
        background: rgba(0, 0, 0, 5%) !important;
        color: #333 !important;
        border: none;
      }

      .ant-select-arrow {
        display: none;
      }
    }

    .vben-basic-table .ant-table-wrapper {
      margin: 0;
      padding: 12px 0 0;

      .ant-table-thead {
        height: 36px;

        & > tr .ant-table-selection-column::before {
          display: none;
        }
      }

      .ant-table-row {
        height: 36px;
      }

      .ant-table-cell {
        padding: 0 !important;
      }
    }
  }
</style>
