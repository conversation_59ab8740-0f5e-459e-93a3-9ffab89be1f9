<template>
  <div
    class="factory-select"
    :class="theme"
    v-if="factoryInfoList?.length && factoryInfoList.length > 1"
  >
    <Select
      v-model:value="factoryId"
      :bordered="false"
      placeholder="暂无水厂"
      :dropdownStyle="{
        padding: '4px',
      }"
      :popupClassName="`dropdown-factory-select dropdown-factory-select-${theme}`"
      :dropdownMatchSelectWidth="false"
      @change="changeFactory"
    >
      <SelectOption v-for="item in factoryInfoList" :key="item.factoryId" :value="item.factoryId">
        {{ item.factoryName }}
      </SelectOption>
      <template #suffixIcon>
        <Icon class="select-suffix-icon" icon="icon-park-outline:down" :size="20" />
      </template>
    </Select>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { Select, SelectOption } from 'ant-design-vue';
  import { useUserStore } from '/@/store/modules/user';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import { useEventBus } from '@vueuse/core';
  import { debounce } from 'lodash-es';
  // import { usePermission } from '/@/hooks/web/usePermission';
  // import { getCurrentUserFactoryList } from '/@zhcz/api/patrol';
  // import { useRouter } from 'vue-router';

  defineProps({
    theme: {
      type: String,
      default: '',
    },
  });

  const eventBus = useEventBus(FACTORY_KEY);
  const { refreshPage } = useTabs();
  const userStore = useUserStore();
  const userInfo = computed(() => userStore.getUserInfo);
  const factoryInfoList = computed(() => userInfo.value.factoryInfoList || []);
  const factoryId = computed(() => userStore.getCurrentFactoryId);
  const changeFactory = debounce(async (value) => {
    userStore.setFactoryId(value);
    userStore.setGLobalSource({ factoryId: value });
    await refreshPage();
  }, 200);

  eventBus.on(changeFactory);

  // const router = useRouter();
  // const { permissionStore } = usePermission();
  // const getFactoryList = async () => {
  //   const moduleId = permissionStore.getFirstMenuParams?.moduleId;
  //   const res = await getCurrentUserFactoryList({ moduleId });
  //   if (res?.bindSourceUniqueId) {
  //     // 匹配是否启用
  //     let setId = '-1';
  //     const findIndex = res.factoryInfoList.findIndex((item) => item.factoryId === factoryId.value);
  //     setId = findIndex < 0 ? res.bindSourceUniqueId : factoryId.value || setId;
  //     userStore.setFactoryId(setId);
  //     userStore.setUserInfo({
  //       ...userStore.getUserInfo,
  //       factoryInfoList: res.factoryInfoList,
  //       bindSourceUniqueId: setId,
  //     });
  //   } else {
  //     if (!factoryId.value) {
  //       userStore.setFactoryId('-1');
  //       userStore.setGLobalSource({ factoryId: '-1' });
  //     }
  //   }
  // };

  // watch(
  //   () => router.currentRoute.value.path,
  //   async () => {
  //     await getFactoryList();
  //   },
  //   {
  //     deep: true,
  //     immediate: true,
  //   },
  // );
</script>

<style lang="less">
  .ant-select-dropdown {
    &.dropdown-factory-select {
      .ant-select-item-option-selected {
        background-color: @theme-color !important;
        color: #fff !important;
        font-weight: 400;
      }

      .ant-select-item {
        padding: 0 12px;
        line-height: 30px;
      }

      .ant-empty-image {
        height: 62px;
      }
    }

    &.dropdown-factory-select-dark {
      background: #253343;

      .ant-select-item-option {
        background: transparent;
        color: #fff;
      }

      .ant-select-item-option-active {
        background: rgba(255, 255, 255, 0.16);
      }
    }
  }
</style>

<style lang="less" scoped>
  .factory-select {
    position: relative;
    z-index: 1;
    margin: 0 8px 0 14px;
    line-height: 1;

    :deep(.ant-select) {
      position: relative;
      line-height: 30px;
      font-size: 14px;

      &.ant-select-focused {
        .ant-select-selector {
          box-shadow: none !important;
        }
      }

      &:hover {
        .ant-select-selector {
          box-shadow: none !important;
        }
      }

      .ant-select-arrow {
        inset-inline-end: 10px;
        height: 20px;
        margin-top: -9px;
      }

      // &.ant-select-single.ant-select-open {
      //   .select-suffix-icon {
      //     // color: #999;
      //   }
      // }

      .ant-select-selector {
        position: relative;
        min-width: 110px;
        background-color: transparent !important;
        border: 1px solid rgba(255, 255, 255, 0.44) !important;
        box-shadow: none;
        line-height: 30px;
        height: 30px;
        font-size: 16px;
        padding: 0 11px 0 12px;
        z-index: 1;
        // background-color: transparent;

        .ant-select-selection-item {
          font-weight: 400;
          line-height: 30px;
          font-size: 14px;
        }
      }
    }

    &.dark {
      :deep(.ant-select) {
        .ant-select-selector {
          border: 1px solid rgba(255, 255, 255, 0.44) !important;

          .ant-select-selection-item {
            color: #fcfcfc;
          }
        }

        &:hover {
          .ant-select-selector {
            background: rgba(255, 255, 255, 0.16) !important;
          }
        }

        .ant-select-arrow {
          .select-suffix-icon {
            color: #fcfcfc;
          }
        }
      }
    }

    &.light {
      :deep(.ant-select) {
        .ant-select-selector {
          border: 1px solid @aoa3-join-border !important;

          .ant-select-selection-item {
            color: #444444;
          }
        }

        &:hover {
          .ant-select-selector {
            background: var(--theme-color-20p) !important;
            border: 1px solid var(--theme-color-20p) !important;

            // .ant-select-selection-item {
            //   // color: var(--theme-color);
            // }
          }
        }

        .ant-select-arrow {
          .select-suffix-icon {
            color: #444;
          }
        }
      }
    }
  }
</style>
