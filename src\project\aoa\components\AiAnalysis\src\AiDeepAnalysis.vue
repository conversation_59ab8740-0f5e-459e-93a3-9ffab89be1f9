<template>
  <div class="ai-box h-full" ref="aiBoxRef">
    <div class="ai-content">
      <div class="ai-question-box flex flex-row-reverse" v-if="aiQuestion && step >= 1">
        <div class="ai-question p-3">{{ aiQuestion }}</div>
      </div>
      <div class="ai-answer flex mt-6" v-if="step >= 2">
        <div class="logo-box flex items-center justify-center">
          <img :src="deepseekLogo" alt="" />
        </div>
        <div class="ai-answer-right flex-1 pl-3">
          <div class="depth-des flex items-center">
            <img class="mr-1" :src="deeplyLogo" alt="" />
            <span>{{ thinkDes }}</span>
            <!-- <span v-if="!loading">（用时{{ duration }}s）</span> -->
          </div>
          <template v-if="aiData.deepAnalysis.length">
            <div
              class="deep-analysis pl-2.5 markdown"
              v-html="renderMdText(aiData.deepAnalysis)"
            ></div>
          </template>
          <template v-if="aiData.analysisContent.length">
            <div class="explain markdown" v-html="renderMdText(aiData.analysisContent)"></div>
          </template>
          <div class="relative mt-6" style="width: 24px" v-loading="loading"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import deepseekLogo from '/@/assets/images/deepseek-logo_white.png';
  import deeplyLogo from '/@/assets/images/deeply-logo_white.png';
  import { setTimer } from './helper/index';
  import MarkdownIt from 'markdown-it';

  const emits = defineEmits(['setScroll']);

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    speed: {
      type: Number,
      default: 1,
    },
    aiQuestion: {
      type: String,
      default: '',
    },
    aiData: {
      type: Object,
      default: () => {
        return {
          deepAnalysis: '',
          analysisContent: '',
        };
      },
    },
    duration: {
      type: Number,
      default: 0,
    },
  });

  const aiBoxRef = ref<HTMLElement>();
  const step = ref(0); // 0: init, 1: deep analysis, 2: result
  // const time = ref(0);
  const finallyed = ref(false);
  const hasScroll = ref(false);

  const markdownRender = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
  });

  const thinkDes = computed(() => {
    return props.loading ? '思考中...' : '已深度思考';
  });

  const initFun = async () => {
    await setTimer(500 * props.speed, null);
    step.value++;
    await setTimer(300 * props.speed, null);
    step.value++;
    finallyed.value = true;
  };

  function renderMdText(text) {
    return markdownRender.render(text);
  }

  watch(
    () => props.aiData,
    () => {
      if (!hasScroll.value) {
        const pH = document.querySelector('.ai-analysis-modal .warp-box')?.clientHeight ?? 0;
        const cH = aiBoxRef.value?.scrollHeight ?? 0;
        if (cH > pH) {
          hasScroll.value = true;
          emits('setScroll');
        }
      }
    },
    { deep: true },
  );

  onMounted(() => {
    initFun();
  });
</script>
<style lang="less" scoped>
  .ai-content {
    color: #fff;
    padding-bottom: 50px;
    text-align: justify;

    .logo-box {
      .px2vw(40);
      width: @vw;
      height: @vw;
      border: 1px solid rgba(158, 197, 191, 0.72);
      border-radius: 50%;

      img {
        .px2vw(24);
        width: @vw;
        height: @vw;
      }
    }

    .ai-question-box {
      .ai-question {
        .px2vw(40);
        width: calc(100% - @vw - 0.75rem);
        .font-size(16);
        .width-prop(24, line-height);
        border-radius: 4px;
        text-align: justify;
        background: rgba(0, 43, 25, 0.56);
        border: 1px solid rgba(255, 255, 255, 0.16);
        color: #fff;
      }
    }

    .ai-answer-right {
      :deep(.full-loading) {
        background-color: transparent !important;
      }

      .depth-des {
        width: fit-content;
        .width-prop(36, height);
        .px2vw(12);
        margin: 2px 0;
        padding: 0 @vw;
        border-radius: 4px;
        background: rgba(0, 43, 25, 0.56);
        border: 1px solid rgba(255, 255, 255, 0.16);
        .font-size(15);
        color: #fff;

        img {
          .px2vw(16);
          width: @vw;
          height: @vw;
        }
      }

      .deep-analysis {
        .px2vw(10);
        color: rgba(255, 255, 255, 0.88);
        text-align: justify;
        .width-prop(22, line-height);
        margin: @vw 0;
        .font-size(15);
        border-left: 2px solid rgba(255, 255, 255, 0.4);
      }

      .explain {
        .font-size(15);
        .width-prop(22, line-height);
        color: rgba(255, 255, 255, 1);
      }

      :deep(.markdown p) {
        .px2vw(10);
        margin: @vw 0 !important;
        .width-prop(22, line-height) !important;
      }

      :deep(.markdown li) {
        .width-prop(24, line-height) !important;
      }
    }
  }
</style>
