<template>
  <div class="chart w-full h-full" ref="chartRef" v-loading="loading"></div>
</template>

<script setup lang="ts">
  // import dayjs from 'dayjs/esm';
  import { computed } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { ref, Ref, PropType, nextTick, watch } from 'vue';
  import { useChartsData, type Params } from './useChartsData';
  import { uniqBy, values, flatMap } from 'lodash-es';
  import { getMaxValue, getMinValue, getChartsGrid, colorList, mockTime } from '/@aoa/utils';
  // import { TimeTypeMap } from '/@aoa/components/SearchForm';
  import { getValue } from '/@aoa/utils/string';

  const props = defineProps({
    api: {
      type: Function,
      required: true,
    },
    params: {
      type: Object as PropType<Params>,
      required: true,
    },
  });

  const chartRef = ref<HTMLDivElement | null>(null);
  const { chartsData, loading } = useChartsData(props);

  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  const isEmpty = computed(() => {
    if (loading.value) return false;
    const allDatas = flatMap(values(chartsData), 'datas');
    return !allDatas.length;
  });

  const getSortData = (data: any[]) => {
    // 1. 按照 indicatorCode 分组
    const groupedData = data.reduce((acc, item) => {
      const key = item.indicatorCode;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {});

    // 2. 对每个分组进行排序
    const sortedData = Object.values(groupedData).flatMap((group: any) => {
      return group.sort((a, b) => {
        const orderA = a.indexTitie.includes('同比') ? 1 : a.indexTitie.includes('环比') ? 2 : 0;
        const orderB = b.indexTitie.includes('同比') ? 1 : b.indexTitie.includes('环比') ? 2 : 0;
        return orderA - orderB;
      });
    });
    return sortedData;
  };

  async function renderEcharts() {
    if (isEmpty.value) return;
    await nextTick();

    // const { format, format2 } = TimeTypeMap[props.params.timeType];
    const { nowData, sameData, compareData } = chartsData;

    const dataArr = [
      { ...nowData, suffix: '', option: {} },
      { ...compareData, suffix: '-环比', option: {} },
      { ...sameData, suffix: '-同比', option: {} },
    ];

    const firstUniqUnit = uniqBy(nowData.datas, 'unit');
    const allMax = getMaxValue(nowData.datas);
    const { gridLeft, gridRight } = getChartsGrid(firstUniqUnit, allMax);

    const yAxisList = firstUniqUnit.map((val, key) => {
      const mergeDatas = dataArr
        .map((item) => item.datas.filter((i) => i.unit === val.unit))
        .flat()
        .map((i) => i.data)
        .flat();

      const max = getMaxValue(mergeDatas);
      const min = getMinValue(mergeDatas);

      let offset = Math.floor(key / 2) * 72;
      return {
        offset,
        position: key % 2 == 0 ? 'left' : 'right',
        unit: val.unit,
        type: 'value',
        name: val.unit ? ` 单位(${val.unit})` : '',
        min,
        max,
        splitNumber: 5,
        interval: (max - min) / 5,
        nameTextStyle: {
          color: '#999999',
          align: firstUniqUnit.length > 2 ? (key % 2 == 0 ? 'right' : 'left') : 'center',
          padding: max.toString().length > 3 ? [0, 20, 0, 0] : [0, 0, 0, 0],
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#333',
          fontSize: '12px',
          formatter: (value: string) => {
            return getValue(value, val.unit);
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A1D0C4',
            type: [5, 10],
            offset: 5,
          },
        },
      };
    });

    const xAxisList = dataArr.map((item, index) => {
      return {
        type: 'category',
        boundaryGap: false,
        axisTick: {
          show: false,
        },
        position: 'bottom',
        offset: index * 35,
        nameTextStyle: {
          color: '#999999',
        },
        axisLine: {
          show: index === 0,
          lineStyle: {
            color: '#59B29B',
          },
          // onZeroAxisIndex: index,
        },
        axisPointer: {
          show: true,
          type: 'line',
          lineStyle: {
            color: '#119078',
            type: [5, 10],
          },
        },
        axisLabel: {
          show: true,
          color: '#333',
          fontSize: '12px',
          formatter: (value, index) => {
            if (index === 0) {
              return `{date| ${value}}`;
            }
            return value;
          },
          rich: {
            date: {
              padding: [0, 0, 0, 30],
            },
          },
        },
        data: item.time.length ? item.time : mockTime(),
      };
    });

    const mergeSeriesData = dataArr
      .map((item) => {
        return item.datas.map((i) => ({
          ...i,
          indexTitie: `${i.indexTitie}${item.suffix}`,
        }));
      })
      .flat();
    const sortData = getSortData(mergeSeriesData);
    console.log(sortData);
    const seriesData = mergeSeriesData.map((item, index) => ({
      color:
        dataArr[0].datas.length <= colorList.length
          ? colorList[index % dataArr[0].datas.length]
          : colorList[(index % dataArr[0].datas.length) % colorList.length],
      name: item.indexTitie,
      type: 'line',
      smooth: true,
      symbol: 'none',
      symbolSize: 6,
      data: item.data,
      xAxisIndex: item.indexTitie.includes('同比') ? 1 : item.indexTitie.includes('环比') ? 2 : 0,
      yAxisIndex:
        yAxisList.findIndex((val) => val.unit == item.unit) < 0
          ? 0
          : yAxisList.findIndex((val) => val.unit == item.unit),
    }));

    const option: any = {
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        axisPointer: {
          animation: false,
        },
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
        formatter: (params: Recordable[]) => {
          const datas = params.filter(
            (item) => !item.seriesName.includes('同比') && !item.seriesName.includes('环比'),
          );
          const paramsDataList = datas.map((item) => {
            const tbParams = params.find((i) => i.seriesName === `${item.seriesName}-同比`);
            const hbParams = params.find((i) => i.seriesName === `${item.seriesName}-环比`);
            return [item, tbParams, hbParams];
          });
          const thead = `
              <table style="text-align: left;">
                <thead>
                  <tr>
                    <th style="color: #333">${paramsDataList[0][0]?.axisValue}</th>
                    <th style="min-width: 50px;"></th>
                    <th style="min-width: 100px;color: #333">当前</th>
                    <th style="min-width: 100px;color: #333">环比</th>
                    <th style="min-width: 100px;color: #333">同比</th>
                  </tr>
                </thead>
                <tbody>
              `;
          const tbody = paramsDataList
            .map((item) => {
              const unit =
                dataArr[0].datas?.find((i) => item[0]?.seriesName?.includes(i.indexTitie))?.unit ||
                '';
              return `<tr>
                  <td>${item[0]?.marker}&nbsp;${item[0]?.seriesName}</td>
                  <td style="min-width: 50px;"></td>
                  <td style="min-width: 100px;line-height: 2">
                    <span style="color: #333;font-weight: 600">${getValue(
                      item[0]?.value,
                      unit,
                    )}</span>&nbsp;&nbsp;
                    ${
                      item[0]?.value !== null &&
                      item[0]?.value !== undefined &&
                      item[0]?.value !== ''
                        ? unit
                        : ''
                    }
                  </td>
                  <td style="min-width: 100px;line-height: 2">
                    <span style="color: #333 ;font-weight: 600">${getValue(
                      item[1]?.value,
                      unit,
                    )}</span>&nbsp;&nbsp;
                    ${
                      item[1]?.value !== null &&
                      item[1]?.value !== undefined &&
                      item[1]?.value !== ''
                        ? unit
                        : ''
                    }
                  </td>
                  <td style="min-width: 100px;line-height: 2">
                    <span style="color: #333 ;font-weight: 600">${getValue(
                      item[2]?.value,
                      unit,
                    )}</span>&nbsp;&nbsp;
                    ${
                      item[2]?.value !== null &&
                      item[2]?.value !== undefined &&
                      item[2]?.value !== ''
                        ? unit
                        : ''
                    }
                  </td>
                </tr>`;
            })
            .join('');
          return `${thead}${tbody}</tbody></table>`;
        },
      },
      legend: {
        show: true,
        type: 'scroll',
        icon: 'circle',
        left: 0,
        itemWidth: 8,
        itemHeight: 8,
        padding: [
          0,
          gridRight + 80 * Math.floor(yAxisList.length / 2),
          0,
          gridLeft + (yAxisList.length > 2 ? 65 * Math.ceil(yAxisList.length / 2) : 85),
        ],
        textStyle: {
          color: '#999999',
          fontSize: '12px',
        },
        top: 5,
      },
      grid: {
        left: gridLeft + 2,
        right: gridRight + 8,
        bottom: 35,
        top: 32,
        containLabel: true,
      },
      xAxis: xAxisList,
      yAxis: yAxisList,
      series: seriesData,
    };

    setOptions(option);
  }

  watch(chartsData, renderEcharts, {
    deep: true,
  });
</script>

<style scoped lang="less">
  .chart {
    padding: 16px;
    overflow: hidden;
  }
</style>
