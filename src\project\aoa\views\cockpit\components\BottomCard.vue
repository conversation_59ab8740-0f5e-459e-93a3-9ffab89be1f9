<template>
  <div class="water-card water-card-container">
    <div class="water-box">
      <div class="item" v-for="(item, index) in data" :key="index">
        <div class="icon-box"></div>
        <div class="info">
          <div class="value-unit">
            <div class="value" @click="openModal(item.indexCode, groupCode)">
              <Tooltip>
                <template #title>
                  {{
                    item.value === '' || item.value === null
                      ? '-'
                      : `${formatNumber(Number(item.value), item.digit).num}${
                          formatNumber(Number(item.value), item.digit).level
                        }${item.unit}`
                  }}
                </template>
                <span class="text">{{
                  item.value === '' || item.value === null
                    ? '-'
                    : formatNumber(Number(item.value), item.digit).num
                }}</span>
                <span class="level">{{ formatNumber(Number(item.value), item.digit).level }}</span>
              </Tooltip>
            </div>
            <div class="unit">{{ item.value === '' || item.value === null ? '' : item.unit }}</div>
          </div>
          <div class="name" @click="openModal(item.indexCode, groupCode)">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>

    <IndicatorModal
      v-model:open="indicatorOpen"
      :width="isIPCRef ? '100%' : '1272px'"
      :bodyStyle="modalBodyStyle"
      :destroyOnClose="true"
      :groupInfo="groupInfo"
      :themeColor="themeColor"
      :echartsConfig="echartsConfig"
      :multiple="multiple"
      :factoryId="factoryId"
      title="指标详情"
      :requestHeader="requestHeader"
      :footer="modalFooter"
      :base-url="baseUrl"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, PropType } from 'vue';
  import type { DataList } from '../type';
  import { formatNumber } from '/@aoa/utils/number';
  import { Tooltip } from 'ant-design-vue';
  import { IndicatorModal } from 'hlxb-business-ui';
  import { getProcessEditorTheme } from '/@process-editor/assets/theme';
  import { isIPCBreakPoint } from '/@/hooks/event/useBreakpoint';
  import { useWindowSize } from '@vueuse/core';
  import { getToken } from '/@/utils/auth';
  import { useDomain } from '/@/locales/useDomain';
  import { getFactoryId } from '/@aoa/utils/factory';
  import { getAppEnvConfig } from '/@/utils/env';

  defineProps({
    data: {
      type: Array as PropType<DataList[]>,
      default: () => [],
    },
    groupCode: String,
  });

  const { themeColor, echartsConfig } = getProcessEditorTheme();
  const factoryId = getFactoryId();
  const { isIPCRef } = isIPCBreakPoint();
  const { height } = useWindowSize();
  const modalBodyStyle = computed(() => {
    return isIPCRef.value
      ? {
          height: `${height.value - 64}px`,
        }
      : { height: '716px' };
  });
  const modalFooter = computed(() => {
    return isIPCRef.value ? null : ' ';
  });
  const baseUrl = computed(() => getAppEnvConfig()?.VITE_GLOB_API_URL);

  const { getTenantId } = useDomain();
  const requestHeader: any = {
    Authorization: getToken(),
    'Tenant-Id': getTenantId.value,
  };

  const indicatorOpen = ref(false);
  const groupInfo = ref({
    groupCode: '',
    resourceInterfaceId: '3',
    jsConvert: false,
    indexCodes: '',
  });
  const multiple = ref(false);

  const openModal = (code, groupCode) => {
    groupInfo.value.indexCodes = code;
    groupInfo.value.groupCode = groupCode;
    indicatorOpen.value = true;
  };
</script>

<style lang="less" scoped>
  .water-card {
    position: absolute;
    .width-prop(10, bottom);
    left: 50%;
    .px2vw(100);
    .width-prop(860);
    height: @vw;
    transform: translateX(-50%);
    background-image: url('/@aoa/views/cockpit/assets/images/icon_10.png');
    background-size: 100% auto;
    background-position: center bottom;
    background-repeat: no-repeat;
    z-index: 5;

    .water-box {
      // padding-bottom: 17px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: flex-end;
      .px2vw(20);
      gap: 0 @vw;

      .item {
        .width-prop(20, padding-left);
        background-image: url('/@aoa/views/cockpit/assets/images/icon_5.png');
        background-size: cover;
        background-position: center bottom;
        background-repeat: no-repeat;
        .px2vw(72);
        .width-prop(252);
        height: @vw;
        display: flex;
        align-items: center;

        &:nth-child(2) {
          .icon-box {
            background-image: url('/@aoa/views/cockpit/assets/images/icon_7.png');
          }
        }

        &:nth-child(3) {
          .icon-box {
            background-image: url('/@aoa/views/cockpit/assets/images/icon_8.png');
          }
        }

        .icon-box {
          .px2vw(44);
          .width-prop(44);
          height: @vw;
          background-image: url('/@aoa/views/cockpit/assets/images/icon_6.png');
          background-size: cover;
          background-position: center bottom;
          background-repeat: no-repeat;
        }

        .info {
          .width-prop(12, padding-left);
        }

        .name {
          .width-prop(3, padding-top);
          white-space: nowrap;
          cursor: pointer;
          .font-size(16);
          line-height: 1;
          text-shadow: 0px 3px 3px #023a30;
        }

        .value-unit {
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .value {
            font-family: Alimama ShuHeiTi;
            font-weight: 500;
            .font-size(24);
            line-height: 1;
            text-shadow: 0px 3px 3px #023a30;

            .level {
              .font-size(22);
            }
          }

          .unit {
            .width-prop(4, padding-left);
            .font-size(16);
            text-shadow: 0px 3px 3px #023a30;
          }
        }
      }
    }
  }
</style>
