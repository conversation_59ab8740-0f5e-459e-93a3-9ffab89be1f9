import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getPlatformInfoList } from '/@zhcz/api/config-center/scenes-group';
import { getDictTypeListApi } from '/@/api/admin/dict';
import { RESOURCE_TYPE, TOKEN_TYPE, DICT } from '/@zhcz/enums/sceneResource';

export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'indexName',
  },
  {
    title: '平台',
    dataIndex: 'platformName',
  },
  {
    title: '类型',
    dataIndex: 'resourceTypeName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'platformId',
    component: 'ApiSelect',
    label: '资源平台',
    labelWidth: 68,
    colProps: { span: 6 },
    componentProps: {
      api: getPlatformInfoList,
      labelField: 'displayName',
      valueField: 'id',
    },
  },
  {
    field: 'indexName',
    component: 'Input',
    label: '名称',
    colProps: { span: 6 },
  },
  {
    field: 'resourceType',
    label: '来源类型',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.RESOURCE_TYPE },
      valueField: 'intValue',
    },
    colProps: { span: 6 },
  },
];

export const schemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'platformId',
    component: 'ApiSelect',
    label: '平台',
    required: true,
    colProps: { span: 12 },
    componentProps: ({ formModel }) => {
      return {
        api: getPlatformInfoList,
        resultField: 'records',
        labelField: 'displayName',
        valueField: 'id',
        onChange: async (value, option) => {
          if (!option) {
            const platformList = await getPlatformInfoList();
            option = platformList.find((item) => item.id === value);
          }

          formModel.indexSource = option.indexSource;
          formModel.tokenType = option.tokenType;
          formModel.tokenName = option.tokenName;
          formModel.tokenLocation = option.tokenLocation;
          formModel.platformToken = option.platformToken;
          formModel.tokenRefreshTime = option.tokenRefreshTime;
          formModel.tokenFunction = option.tokenFunction;
        },
      };
    },
  },
  {
    field: 'resourceType',
    label: '来源类型',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.RESOURCE_TYPE },
      valueField: 'intValue',
    },
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'indexName',
    component: 'Input',
    label: '来源名称',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'indexSource',
    label: '接口类型',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.INDEX_SOURCE_TYPE },
      valueField: 'intValue',
      disabled: true,
    },
    required: true,
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType !== RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'tokenType',
    label: 'token类型',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.TOKEN_TYPE_DICT },
      valueField: 'intValue',
      disabled: true,
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType !== RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'tokenName',
    component: 'Input',
    label: 'token名称',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return (
        values.tokenType &&
        values.tokenType !== TOKEN_TYPE.NONE &&
        values.resourceType !== RESOURCE_TYPE.DATABASE
      );
    },
  },
  {
    field: 'tokenLocation',
    label: 'token位置',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.TOKEN_LOCATION },
      valueField: 'intValue',
      disabled: true,
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return (
        values.tokenType &&
        values.tokenType !== TOKEN_TYPE.NONE &&
        values.resourceType !== RESOURCE_TYPE.DATABASE
      );
    },
  },
  {
    field: 'platformToken',
    component: 'Input',
    label: 'token值',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
    },
    ifShow: ({ values }) => {
      return (
        values.tokenType &&
        values.tokenType !== TOKEN_TYPE.NONE &&
        values.resourceType !== RESOURCE_TYPE.DATABASE
      );
    },
  },
  {
    field: 'tokenRefreshTime',
    component: 'InputNumber',
    label: '刷新时间',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
    },
    ifShow: ({ values }) => {
      return (
        values.tokenType &&
        values.tokenType === TOKEN_TYPE.DYNAMIC &&
        values.resourceType !== RESOURCE_TYPE.DATABASE
      );
    },
  },
  {
    field: 'tokenFunction',
    component: 'InputTextArea',
    label: 'token函数',
    colProps: { span: 24 },
    componentProps: {
      rows: 4,
      disabled: true,
    },
    ifShow: ({ values }) => {
      return (
        values.tokenType &&
        values.tokenType === TOKEN_TYPE.DYNAMIC &&
        values.resourceType !== RESOURCE_TYPE.DATABASE
      );
    },
  },
  {
    field: 'paramConstant',
    // component: 'InputTextArea',
    label: '常量',
    // slot: 'param-constant-slot',
    // colProps: { span: 24 },
    // componentProps: {
    //   rows: 4,
    // },
    component: 'CodeEditor',
    colProps: { lg: 24 },
    defaultValue: '{}',
    componentProps: () => {
      return {
        rows: 4,
        class: 'h-40',
        mode: 'JSON',
      };
    },
    ifShow: ({ values }) => {
      return values.resourceType !== RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'databaseType',
    label: '数据库类型',
    component: 'ApiSelect',
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.DATABASE_TYPE },
      valueField: 'intValue',
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'databaseIp',
    component: 'Input',
    label: '主机地址',
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
    rules: [
      {
        validator: async (_rule, value) => {
          const reg =
            /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
          if (value && !reg.test(value)) {
            return Promise.reject('请输入正确的主机地址(IPV4)');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'databasePort',
    component: 'Input',
    label: '端口',
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
    rules: [
      {
        validator: async (_rule, value) => {
          const reg = /^([0-9]{1,5})$/;
          if (value && !reg.test(value)) {
            return Promise.reject('请输入正确的端口(0-65535)');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'databaseUsername',
    component: 'Input',
    label: '用户名',
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
  },
  {
    field: 'databasePassword',
    component: 'Input',
    label: '密码',
    colProps: { span: 12 },
    ifShow: ({ values }) => {
      return values.resourceType === RESOURCE_TYPE.DATABASE;
    },
  },
];
