import borderImg from '../../assets/images/border.png';
import videoImg from '../../assets/images/video-two.png';
import lineChartImg from '../../assets/images/line-chart-bg.jpg';
import tableImg from '../../assets/images/table-bg.jpg';
import cardBg1 from '../../assets/images/card-bg1.png';
import cardBg2 from '../../assets/images/card-bg2.png';
import sendingImg from '../../assets/images/sending.gif';
import successImg from '../../assets/images/success.jpg';
import failureImg from '../../assets/images/failure.jpg';
import timeoutImg from '../../assets/images/timeout.jpg';
import { createLocalStorage } from '/@/utils/cache';
import { TENANTID_KEY } from '/@/enums/cacheEnum';
import { getIndexListApi } from '../../api/index';
import dayjs from 'dayjs';
import { groupBy, cloneDeep, get, omit } from 'lodash-es';
import { buildShortUUID } from '/@/utils/uuid';
import { frameBackgroundTypeEnum, expressionTypeEnum } from './enum';
import {
  FRAMETYPE_ENUM,
  PLACEMEN_ENUM,
  FRAME_PADDING,
  CONTROL_INDEX_VALUE,
  CONTROL_INDEX_TEXT,
  COMMON_KIT,
} from './constant';
import { LockState } from '@meta2d/core';
import { getColorByIndicatorItem } from '/@process-editor/utils';
import {
  INDICATOR_TYPE_ENUM,
  CONTROL_SWITCH_DEFAULT_SETTING,
  VIDEO_IMG_WIDTH,
  VIDEO_IMG_HEIGHT,
} from '/@process-editor/constant';

import {
  KIT_INDEX_NAME,
  KIT_INDEX_VALUE,
  KIT_INDEX_UNIT,
  KIT_INDEX_BORDER,
  KIT_INDEX_BORDER_TITLE,
  DASHED_BORDER_MIN_WIDTH,
  COL_MIN_WIDTH,
  COL_MIN_HEIGHT,
  TABLE_HEADER_MIN_HEIGHT,
  COLOR,
  TITLE_COLOR,
  TABLE_KIT_INDEX_BORDER_TITLE,
  TABLE_KIT_INDEX_VALUE,
  TABLE_KIT_FORM_ITEM,
  ITEM_TYPES,
  FONT_SIZE,
  FONT_SIZE_MEDIUM,
  FONT_SIZE_LARGE,
  LIGHT_COLOR,
  INPUT_BORDER_COLOR,
  DISTRIBUTE_STATUS,
  CHART_WIDTH,
  CHART_HEIGHT,
  DIAGNOSTIC_ANALYSIS_EVENT,
  DIAGNOSTIC_MOUSELEAVE_EVENT,
} from './constant';
import { getImgSize } from '../share';
import { Position, TableStyle, DistributeStatus } from './types';
import { isIPCBreakPoint } from '/@/hooks/event/useBreakpoint';
import type { Meta2dData } from 'hlxb-meta2d-core';
import { addResourcePrefix } from '../../utils/index';
import { getFactoryId } from '/@process-editor/utils';
import { EventAction } from 'hlxb-meta2d-core';
import { parseJson } from '/@/utils';
import { isObject } from '/@/utils/is';

// 数据处理层

const grid = {
  top: 100,
  bottom: 30,
  left: 35,
  right: 35,
};

const { isIPCRef } = isIPCBreakPoint();
const legend = {
  // icon: 'pin',
  icon: 'path://M150,100 A50,50 0 1,0 50,100 A50,50 0 1,0 150,100 Z',
  itemWidth: 25,
  itemHeight: 14,
  textStyle: {
    fontSize: FONT_SIZE,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  selected: {
    // Lowest: false,
  },
};

const lineXAxis = {
  type: 'category',
  data: [],
  axisLine: {
    show: true,
    lineStyle: {
      color: 'rgba(255, 255, 255, 0.6)',
      type: 'dashed',
    },
  },
  axisTick: {
    show: false,
  },
  axisLabel: {
    textStyle: {
      color: 'rgba(255, 255, 255, 0.6)',
      fontSize: FONT_SIZE,
    },
  },
};

const lineYAxis = {
  name: '',
  nameTextStyle: {
    fontSize: FONT_SIZE,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  type: 'value',
  splitLine: {
    show: true,
    lineStyle: {
      color: 'rgba(255,255,255,0.2)',
      type: 'dashed',
    },
  },
  axisLine: {
    show: false,
    lineStyle: {
      color: 'rgba(255, 255, 255, 0.6)',
    },
  },
  axisTick: {
    show: false,
  },
  axisLabel: {
    show: true,
    textStyle: {
      color: 'rgba(255, 255, 255, 0.6)',
      fontSize: FONT_SIZE,
    },
  },
};

const lineSeries = {
  data: [],
  type: 'line',
  smooth: true,
  symbol: 'none',
  name: '',
  yAxisIndex: 0,
};

/**
 * 获取指标值颜色
 * @param {Object} record 指标图元
 * @returns color 颜色
 */
function getNumberColor(record: Recordable, defaultColor) {
  function getColorNotExpression() {
    const value = Number(record.expression);
    let color = record.normalColor || defaultColor || '#fff';

    if (record.upperLimits === '' && record.lowerLimits === '') {
      return color;
    }

    const upperLimits = Number(record.upperLimits);
    if (!Number.isNaN(upperLimits)) {
      const upperExpression = record.isUpperEqual ? value >= upperLimits : value > upperLimits;
      if (upperExpression) {
        return record.greaterThanUpperColor;
      }
    }

    const lowerLimits = Number(record.lowerLimits);
    if (!Number.isNaN(lowerLimits)) {
      const lowerExpression = record.isLowerEqual ? value <= lowerLimits : value < lowerLimits;
      if (lowerExpression) {
        return (color = record.lowerThanLowerColor);
      }
    }
    return color;
  }

  //  script scriptResult scriptType warningRuleId
  const NORMAL = 'normal';
  function getColorByIndicator() {
    return record.scriptResult && record.scriptResult !== NORMAL
      ? record.scriptResult
      : getColorNotExpression();
  }

  function getColorByWarningRule() {
    return record.scriptResult && record.scriptResult !== NORMAL
      ? record.scriptResult
      : getColorNotExpression();
  }

  let color = '';
  switch (record.scriptType) {
    case expressionTypeEnum.indicator:
      color = getColorByIndicator();
      break;
    case expressionTypeEnum.rule:
      color = getColorByWarningRule();
      break;
    default:
      color = getColorNotExpression();
      break;
  }

  return color;
}

/**
 * 根据缩放获取位置信息
 * @param data - 包含 flowModelId 的数据对象
 * @param meta2dData - 包含 pens 数组的元数据对象
 * @returns 如果找到了对应的笔，则更新其位置；否则不进行任何操作
 */
function getPositionByScale(data: Recordable, meta2dData: Meta2dData | undefined) {
  if (!meta2dData) return;

  const id = data.flowModelId;

  const pen = meta2dData.pens.find((item) => item.id === id);

  if (pen) {
    updateKitPosition(data.framePlacement, data, pen);
  }
}

/**
 * 通过缩放获取字体大小
 * @param {number} size 字体大小
 * @param {number} scale 缩放
 */
function getFontSizeByScale(size: number, scale: number) {
  return isIPCRef.value ? size + 3 : Math.round(size / scale);
}

/**
 * 通过图片类型获取卡片背景图
 * @param frameBackgroundType 卡片背景图类型
 * @param frameBackground 卡片背景图
 * @returns
 */
function getCardBgSrc(frameBackgroundType: number, frameBackground: string) {
  switch (frameBackgroundType) {
    case frameBackgroundTypeEnum.MODE1:
      return cardBg1;
    case frameBackgroundTypeEnum.MODE2:
      return cardBg2;
    case frameBackgroundTypeEnum.CUSTOM:
      return addResourcePrefix(frameBackground);
    default:
      return '';
  }
}

/**
 * 获取指标值
 * @param val 指标值
 */
// function getValue(val: string) {
//   if (Number(val) === -999) {
//     return '-';
//   }
//   return val || '-';
// }

/**
 * 获取画布的生产套件数据
 * @param {Array} data 原生产套件数据
 * @param {number} scale 画布缩放
 * @param {Meta2dData} meta2dData Meta2dData
 * @returns result 画布的生产套件数据
 */
export async function getProductionKitMeta2dData(
  data: Recordable[],
  scale: number,
  meta2dData: Meta2dData | undefined,
) {
  async function getPens(item: Recordable, scale: number) {
    async function getRenderPen(kitItem: Recordable, index: number, item: Recordable) {
      const SimulationData = getPenByFlowModelIdInFlowData(
        meta2dData?.originPens,
        item.flowModelId,
      );
      if (!SimulationData) throw new Error('未找到流程模型数据');
      if (!meta2dData) throw new Error('没有原始数据');
      if (SimulationData && meta2dData) {
        const frameWidth = getWidthByProductPenData(item);
        if (frameWidth) item.frameWidth = frameWidth;
        const frameHeight = getFrameHeightByProductPenData(item);
        if (frameHeight) item.frameHeight = frameHeight;
        const originPositin = meta2d.canvas.getPenRect(
          SimulationData,
          meta2dData.origin,
          meta2dData.scale,
        );
        updateKitPosition(item.framePlacement, item, originPositin);
      }

      const kitConfig = item.businessData.kitConfig;

      function getColPositionX(x: number, index: number) {
        const width = kitConfig?.column?.width || [];
        const show = kitConfig?.column?.show || [];
        const sum = width
          .slice(0, index)
          .filter((_, i) => show[i])
          .reduce((c, n) => c + n, 0);
        return x + sum;
      }

      function getTextAlign(index: number) {
        const textAlign = kitConfig?.column?.textAlign ?? ['left', 'left', 'left'];
        return textAlign[index] || 'left';
      }

      function getPadding(index: number) {
        const padding = { paddingTop: '', paddingRight: '', paddingBottom: '', paddingLeft: '' };
        const paddingList = kitConfig?.column?.padding ?? [padding, padding, padding];

        return paddingList[index] || padding;
      }

      // 方位是上，y递减，否则递增  frameType, framePlacement
      const { positionX, positionY } = item;
      const PEN_HEIGHT = getRowHeightByProductPen(item);
      let y = positionY + PEN_HEIGHT * index;
      isHaveDisplayTitleInProductPen(item) && (y += getTitleHeightByProductPen(item));

      function getNamePen() {
        const color = kitConfig?.column?.color || COLOR;
        const name = kitItem.isShowShort ? kitItem.shortDisplayName : kitItem.displayName;
        const size = kitConfig?.column?.fontSize[0] || 21;
        const fontSize = getFontSizeByScale(size, scale);
        const textAlign = getTextAlign(0);
        const width =
          kitConfig?.column?.width[0] || Number(item.frameWidth) || DASHED_BORDER_MIN_WIDTH;
        const padding = getPadding(0);
        const show = kitConfig?.column?.show ?? [];
        const visible = show[0] ?? true;
        if (!visible) return;

        // 指标名
        const namePen = {
          name: 'text',
          text: name,
          color: color,
          hoverColor: color,
          activeColor: color,
          x: positionX,
          y,
          width,
          height: PEN_HEIGHT,
          textAlign,
          fontSize,
          fontWeight: 400,
          category: item.kitTypeName,
          categoryCN: KIT_INDEX_NAME,
          indexName: '',
          rawData: kitItem,
          locked: LockState.DisableMove,
          disableAnchor: true,
          ...padding,
          visible,
        };

        return namePen;
      }

      function getValuePen() {
        const color = getNumberColor(kitItem, kitConfig?.column?.color);
        const size = kitConfig?.column?.fontSize[1] || 21;
        const fontSize = getFontSizeByScale(size, scale);
        const textAlign = getTextAlign(1);
        const defaultWidth = Number(item.frameWidth) || DASHED_BORDER_MIN_WIDTH;
        const width = kitConfig?.column?.width[1] || defaultWidth * 0.65;
        const padding = getPadding(1);
        const show = kitConfig?.column?.show ?? [];
        const visible = show[1] ?? true;
        if (!visible) return;
        function getValue(val: string) {
          if (Number(val) === -999) {
            return '-';
          }
          return val || '-';
        }

        // 值的类型有数字、字符串 默认是数字
        const valuePen = {
          name: 'text',
          text: getValue(kitItem.expression),
          color,
          hoverColor: color,
          activeColor: color,
          x: getColPositionX(positionX, 1),
          y,
          width,
          height: PEN_HEIGHT,
          textAlign,
          fontSize,
          fontWeight: 500,
          category: item.kitTypeName,
          categoryCN: KIT_INDEX_VALUE,
          isShowIndicator: item.businessData.isShowIndicator,
          indexName: name,
          rawData: kitItem,
          locked: LockState.DisableMove,
          disableAnchor: true,
          ...padding,
          visible,
        };
        if (!item.businessData.isShowIndicator) {
          valuePen['events'] = [
            {
              action: EventAction.Emit,
              fn: null,
              name: 'enter',
              value: DIAGNOSTIC_ANALYSIS_EVENT,
              params: '报警诊断分析移入',
              where: { type: null },
            },
            {
              action: EventAction.Emit,
              fn: null,
              name: 'leave',
              value: DIAGNOSTIC_MOUSELEAVE_EVENT,
              params: '报警诊断分析移出',
              where: { type: null },
            },
          ];
        }

        return valuePen;
      }

      // 单位
      function getUnitPen() {
        const color = kitConfig?.column?.color || COLOR;
        const size = kitConfig?.column?.fontSize[2] || 21;
        const fontSize = getFontSizeByScale(size, scale);
        const textAlign = getTextAlign(2);
        const width =
          kitConfig?.column?.width[2] || Number(item.frameWidth) || DASHED_BORDER_MIN_WIDTH;
        const padding = getPadding(2);
        const show = kitConfig?.column?.show ?? [];
        const visible = show[2] ?? true;
        if (!visible) return;

        const unitPen = {
          name: 'text',
          text: kitItem.unit,
          color: color,
          hoverColor: color,
          activeColor: color,
          x: getColPositionX(positionX, 2),
          y,
          width,
          height: PEN_HEIGHT,
          textAlign,
          fontSize,
          fontWeight: 400,
          category: item.kitTypeName,
          categoryCN: KIT_INDEX_UNIT,
          indexName: '',
          rawData: kitItem,
          locked: LockState.DisableMove,
          disableAnchor: true,
          ...padding,
          visible,
        };

        return unitPen;
      }

      function getTitlePen() {
        let titlePen: any = null;
        const text = item.kitDisplayName;
        const color = kitConfig?.title?.color || TITLE_COLOR;
        const size = kitConfig?.title?.fontSize || 21;
        const fontSize = getFontSizeByScale(size, scale);
        if (index === 0 && isHaveDisplayTitleInProductPen(item)) {
          titlePen = {
            name: 'text',
            text,
            color: color,
            hoverColor: color,
            activeColor: color,
            x: positionX,
            y: positionY,
            width: getWidthByProductPenData(item, false),
            height: getTitleHeightByProductPen(kitItem),
            textAlign: 'center',
            fontSize: fontSize,
            // fontWeight: 600,
            category: item.kitTypeName,
            categoryCN: KIT_INDEX_BORDER_TITLE,
            indexName: '',
            rawData: kitItem,
            locked: LockState.DisableMove,
            disableAnchor: true,
          };
        }

        return titlePen;
      }

      async function getBackgroundPen() {
        let backgroundPen: any = null;
        const frameType = item.frameType;

        if (index === 0 && frameType === 1) {
          // 默认老逻辑
          backgroundPen = {
            name: '边框',
            image: borderImg,
            width: item.frameWidth,
            height: item.frameHeight,
            x: positionX,
            y: positionY,
            isBottom: true,
            category: item.kitTypeName,
            categoryCN: KIT_INDEX_BORDER,
            indexName: '',
            rawData: kitItem,
            locked: LockState.DisableMove,
            disableAnchor: true,
          };

          if (item.frameBackgroundType) {
            const src = getCardBgSrc(item.frameBackgroundType, item.frameBackground);
            // const img = await getImgSize(src);
            backgroundPen.image = src;
            // backgroundPen.width = img.width || item.frameWidth;
            // backgroundPen.height = img.height || item.frameHeight;
          }
        }

        return backgroundPen;
      }

      const result: unknown[] = [];

      const namePen = getNamePen();
      const valuePen = getValuePen();
      const unitPen = getUnitPen();
      const titlePen = getTitlePen();
      const backgroundPen = await getBackgroundPen();

      const pens = [namePen, valuePen, unitPen, titlePen, backgroundPen].filter(Boolean);
      result.push(...pens);

      return result;
    }

    const kit = item.businessData.displayProductDataInfos;
    const resourceInterfaceId = item.businessData.resourceInterfaceId;
    const pens = await Promise.all(
      kit.map(async (kitItem, index) => {
        kitItem.resourceInterfaceId = resourceInterfaceId;
        const result = await getRenderPen(kitItem, index, item);
        return result;
      }),
    );
    if (isHasBorder(item)) {
      const framePadding = getKitPaddingByTablePen(item);
      const offset = { xOffset: framePadding[3], yOffset: framePadding[0] };
      processTableChildrenPenOffset(
        pens.flat().filter((i) => i.name !== '边框'),
        offset,
      );
    }
    testPen(pens.flat().filter((i) => i.name !== '边框'));

    if (meta2dData) {
      pens
        .flat()
        .forEach((i) =>
          Object.assign(i, getRenderRectByPenRect(i, meta2dData?.origin, meta2dData?.scale)),
        );
    }

    return pens;
  }

  let result = await Promise.all(
    data.map(async (item) => {
      const pens = await getPens(item, scale);
      return pens;
    }),
  );

  result = result.flat(2);
  // 处理层级覆盖问题，背景先渲染，层级低
  const backgroundPen = [];
  for (let i = result.length - 1; i >= 0; i--) {
    const item = result[i];
    if (item.name === '边框') {
      backgroundPen.push(item);
      result.splice(i, 1);
    }
  }
  return [...backgroundPen, ...result];
}

/**
 * 获取画布的生产套件数据
 * @param {Array} data 原生产套件数据
 * @param {number} scale 画布缩放
 * @param {Meta2dData} meta2dData Meta2dData
 * @returns result 画布的生产套件数据
 */
export async function getProductionControlKitMeta2dData(
  data: Recordable[],
  scale: number,
  meta2dData: Meta2dData | undefined,
) {
  async function getPens(item: Recordable, scale: number) {
    const SimulationData = getPenByFlowModelIdInFlowData(meta2dData?.originPens, item.flowModelId);
    if (!SimulationData) throw new Error('未找到流程模型数据');
    if (!meta2dData) throw new Error('没有原始数据');
    if (SimulationData && meta2dData) {
      const frameWidth = getWidthByProductPenData(item);
      if (frameWidth) item.frameWidth = frameWidth;
      const frameHeight = getFrameHeightByProductPenData(item);
      if (frameHeight) item.frameHeight = frameHeight;
      const originPositin = meta2d.canvas.getPenRect(
        SimulationData,
        meta2dData.origin,
        meta2dData.scale,
      );
      updateKitPosition(item.framePlacement, item, originPositin);
    }

    async function getRenderPen(kitItem: Recordable, index: number, item: Recordable) {
      const kitConfig = item.businessData.kitConfig;

      function getColPositionX(x: number, index: number) {
        const width = kitConfig?.column?.width || [];
        const show = kitConfig?.column?.show || [];
        const sum = width
          .slice(0, index)
          .filter((_, i) => show[i])
          .reduce((c, n) => c + n, 0);
        return x + sum;
      }

      function getTextAlign(index: number) {
        const textAlign = kitConfig?.column?.textAlign ?? ['left', 'left', 'left'];
        return textAlign[index] || 'left';
      }

      function getPadding(index: number) {
        const padding = { paddingTop: '', paddingRight: '', paddingBottom: '', paddingLeft: '' };
        const paddingList = kitConfig?.column?.padding ?? [padding, padding, padding];

        return paddingList[index] || padding;
      }

      const { positionX, positionY } = item;
      const PEN_HEIGHT = getRowHeightByProductPen(item);
      let y = positionY + PEN_HEIGHT * index;
      isHaveDisplayTitleInProductPen(item) && (y += getTitleHeightByProductPen(item));

      function getNamePen() {
        const color = kitConfig?.column?.color || COLOR;
        const name = kitItem.isShowShort ? kitItem.shortDisplayName : kitItem.displayName;
        const size = kitConfig?.column?.fontSize[0] || 21;
        const isShowUnit = kitConfig?.column?.isShowUnit?.[0] ?? false;
        const fontSize = getFontSizeByScale(size, scale);
        const textAlign = getTextAlign(0);
        const width =
          kitConfig?.column?.width[0] || Number(item.frameWidth) || DASHED_BORDER_MIN_WIDTH;
        const padding = getPadding(0);
        const show = kitConfig?.column?.show ?? [];
        const visible = show[0] ?? true;
        if (!visible) return;

        // 指标名
        const namePen = {
          name: 'text',
          text: name + (isShowUnit ? kitItem.unit : ''),
          color: color,
          hoverColor: color,
          activeColor: color,
          x: positionX,
          y,
          width,
          height: PEN_HEIGHT,
          textAlign,
          fontSize,
          fontWeight: 400,
          category: item.kitTypeName,
          categoryCN: KIT_INDEX_NAME,
          indexName: '',
          rawData: kitItem,
          locked: LockState.DisableMove,
          disableAnchor: true,
          ...padding,
          visible,
        };

        return namePen;
      }

      function getValuePen() {
        const color = getColorByIndicatorItem(kitItem);
        const size = kitConfig?.column?.fontSize[1] || 21;
        const isShowUnit = kitConfig?.column?.isShowUnit?.[1] ?? false;
        const fontSize = getFontSizeByScale(size, scale);
        const textAlign = getTextAlign(1);
        const defaultWidth = Number(item.frameWidth) || DASHED_BORDER_MIN_WIDTH;
        const width = kitConfig?.column?.width[1] || defaultWidth * 0.65;
        const padding = getPadding(1);
        const show = kitConfig?.column?.show ?? [];
        const visible = show[1] ?? true;
        if (!visible) return;
        const { indicatorType } = kitItem;
        function getConfigByIndicatorType(fData) {
          let { expression: value, scriptResult = {} } = fData;
          try {
            scriptResult = JSON.parse(scriptResult) || {};
          } catch (err) {
            scriptResult = {};
            // console.log('-----------------> err', err);
          }

          let res = {};
          switch (indicatorType) {
            case INDICATOR_TYPE_ENUM.NUMBER:
              if (Number(value) === -999) {
                value = '-';
              }
              value = value ? value + (isShowUnit ? kitItem.unit : '') : '-';
              res = { text: value };
              if (!item.businessData.isShowIndicator) res.events = getCursorPointerEvent();
              break;
            case INDICATOR_TYPE_ENUM.TEXT:
              const { color, width: textWidth, height: textHeight } = scriptResult;
              let otherConfig = {};
              if (color) otherConfig = { color, hoverColor: color, activeColor: color };
              if (textWidth) {
                otherConfig = {
                  ...otherConfig,
                  name: 'rectangle',
                  textAlign: 'center',
                  ...getPenPositionByBoxPosition(
                    { x: getColPositionX(positionX, 1), y, width, height: PEN_HEIGHT },
                    { textWidth, height: textHeight || PEN_HEIGHT },
                    textAlign,
                  ),
                };
              }
              if (scriptResult)
                res = Object.assign(
                  { categoryCN: CONTROL_INDEX_TEXT },
                  otherConfig,
                  omit(scriptResult, ['color']),
                );
              break;
            case INDICATOR_TYPE_ENUM.SWITCH:
              res = {
                ...CONTROL_SWITCH_DEFAULT_SETTING,
                checked: scriptResult.checked || 0,
                categoryCN: CONTROL_INDEX_VALUE,
                ...getPenPositionByBoxPosition(
                  { x: getColPositionX(positionX, 1), y, width, height: PEN_HEIGHT },
                  CONTROL_SWITCH_DEFAULT_SETTING,
                  textAlign,
                ),
              };
              if (scriptResult)
                res = {
                  ...res,
                  events: getCursorPointerEvent(),
                  ...omit(scriptResult, ['params']),
                };
              break;
            default:
          }
          return res;
        }

        const valuePen = {
          name: 'text',
          color,
          hoverColor: color,
          activeColor: color,
          x: getColPositionX(positionX, 1),
          y,
          width,
          height: PEN_HEIGHT,
          textAlign,
          fontSize,
          fontWeight: 500,
          category: item.kitTypeName,
          categoryCN: KIT_INDEX_VALUE,
          isShowIndicator: item.businessData.isShowIndicator,
          indexName: name,
          lineWidth: 0,
          rawData: kitItem,
          locked: LockState.DisableMove,
          disableAnchor: true,
          ...getConfigByIndicatorType(kitItem),
          ...padding,
          visible,
        };

        return valuePen;
      }

      // 单位
      function getUnitPen() {
        const color = kitConfig?.column?.color || COLOR;
        const size = kitConfig?.column?.fontSize[2] || 21;
        const fontSize = getFontSizeByScale(size, scale);
        const textAlign = getTextAlign(2);
        const width =
          kitConfig?.column?.width[2] || Number(item.frameWidth) || DASHED_BORDER_MIN_WIDTH;
        const padding = getPadding(2);
        const show = kitConfig?.column?.show ?? [];
        const visible = show[2] ?? true;
        if (!visible) return;

        const unitPen = {
          name: 'text',
          text: kitItem.unit,
          color: color,
          hoverColor: color,
          activeColor: color,
          x: getColPositionX(positionX, 2),
          y,
          width,
          height: PEN_HEIGHT,
          textAlign,
          fontSize,
          fontWeight: 400,
          category: item.kitTypeName,
          categoryCN: KIT_INDEX_UNIT,
          indexName: '',
          rawData: kitItem,
          locked: LockState.DisableMove,
          disableAnchor: true,
          ...padding,
          visible,
        };

        return unitPen;
      }

      function getTitlePen() {
        let titlePen: any = null;
        const text = item.kitDisplayName;
        const color = kitConfig?.title?.color || TITLE_COLOR;
        const size = kitConfig?.title?.fontSize || 21;
        const fontSize = getFontSizeByScale(size, scale);
        if (index === 0 && isHaveDisplayTitleInProductPen(item)) {
          titlePen = {
            name: 'text',
            text,
            color: color,
            hoverColor: color,
            activeColor: color,
            x: positionX,
            y: positionY,
            width: getWidthByProductPenData(item, false),
            height: getTitleHeightByProductPen(kitItem),
            textAlign: 'center',
            fontSize: fontSize,
            // fontWeight: 600,
            category: item.kitTypeName,
            categoryCN: KIT_INDEX_BORDER_TITLE,
            indexName: '',
            rawData: kitItem,
            locked: LockState.DisableMove,
            disableAnchor: true,
          };
        }

        return titlePen;
      }

      async function getBackgroundPen() {
        let backgroundPen: any = null;
        const frameType = item.frameType;

        if (index === 0 && frameType === 1) {
          backgroundPen = {
            name: '边框',
            image: borderImg,
            width: item.frameWidth,
            height: item.frameHeight,
            x: positionX,
            y: positionY,
            isBottom: true,
            category: item.kitTypeName,
            categoryCN: KIT_INDEX_BORDER,
            indexName: '',
            rawData: kitItem,
            locked: LockState.DisableMove,
            disableAnchor: true,
          };

          if (item.frameBackgroundType) {
            const src = getCardBgSrc(item.frameBackgroundType, item.frameBackground);
            backgroundPen.image = src;
          }
        }

        return backgroundPen;
      }

      const result: unknown[] = [];
      try {
        const namePen = getNamePen();
        const valuePen = getValuePen();
        const unitPen = getUnitPen();
        const titlePen = getTitlePen();
        const backgroundPen = await getBackgroundPen();
        // const pens = [namePen, valuePen, unitPen, titlePen, backgroundPen].filter(Boolean);

        const pens = [namePen, valuePen, unitPen, titlePen, backgroundPen].filter(Boolean);
        result.push(...pens);

        return result;
      } catch (error) {
        console.log('-----------------> error', error);
      }
    }

    const kit = item.businessData.displayProductDataInfos;
    const resourceInterfaceId = item.businessData.resourceInterfaceId;
    const pens = await Promise.all(
      kit.map(async (kitItem, index) => {
        kitItem.resourceInterfaceId = resourceInterfaceId;
        const result = await getRenderPen(kitItem, index, item);
        return result;
      }),
    );
    if (isHasBorder(item)) {
      const framePadding = getKitPaddingByTablePen(item);
      const offset = { xOffset: framePadding[3], yOffset: framePadding[0] };
      processTableChildrenPenOffset(
        pens.flat().filter((i) => i.name !== '边框'),
        offset,
      );
    }
    testPen(pens.flat().filter((i) => i.name !== '边框'));

    if (meta2dData) {
      pens
        .flat()
        .forEach((i) =>
          Object.assign(i, getRenderRectByPenRect(i, meta2dData?.origin, meta2dData?.scale)),
        );
    }

    return pens;
  }

  let result = await Promise.all(
    data.map(async (item) => {
      try {
        const pens = await getPens(item, scale);
        return pens;
      } catch (error) {
        console.log('-----------------> error', error);
      }
    }),
  );

  result = result.flat(2);
  // 处理层级覆盖问题，背景先渲染，层级低
  const backgroundPen = [];
  for (let i = result.length - 1; i >= 0; i--) {
    const item = result[i];
    if (item.name === '边框') {
      backgroundPen.push(item);
      result.splice(i, 1);
    }
  }
  return [...backgroundPen, ...result];
}

/**
 * 获取输入框的下控状态图片
 * @param {number} status 状态
 * @returns 图片
 */
export function getInputStatusImg(status: DistributeStatus) {
  const imgMap = new Map([
    ['0', successImg],
    ['1', sendingImg],
    ['2', timeoutImg],
    ['3', failureImg],
  ]);

  return imgMap.get(status) || '';
}

/**
 * 获取画布的table套件数据
 * @param {Array} data 原table套件数据
 * @param {number} scale 画布缩放
 * @param {string} theme 主题
 * @param {Meta2dData} meta2dData Meta2dData
 * @returns result 画布的table套件数据
 */
export async function getTableKitMeta2dData(
  data: Recordable[],
  scale: number,
  theme: string,
  meta2dData: Meta2dData | undefined,
) {
  async function getPens(item: Recordable, scale: number) {
    function getColPositionX(data: Recordable[], index: number, position: Position) {
      let x = position.x;
      if (index > 0) {
        const beforeItems = data.filter((_, _index_) => _index_ < index);
        const width = beforeItems.map((i) => i.width || COL_MIN_WIDTH);
        const sum = width.reduce((c, n) => c + n, 0);
        x += sum;
      }
      return x;
    }

    function getTableTitle(
      name: string,
      position: Position,
      width: number,
      style: TableStyle,
      resourceInterfaceId: string,
    ) {
      const size = Number(style.title?.fontSize) || FONT_SIZE_LARGE;
      const sizeScale = getFontSizeByScale(size, scale);
      const height = getTableTitleHeightByTablePen(item);
      const color = style.title?.color || COLOR;

      const result = {
        name: 'text',
        text: name,
        x: position.x,
        y: position.y,
        width,
        height,
        color: color,
        hoverColor: color,
        activeColor: color,
        disableAnchor: true,
        locked: LockState.DisableMove,
        lineWidth: 0,
        fontSize: sizeScale,
        textAlign: 'left',
        category: item.kitTypeName,
        categoryCN: TABLE_KIT_INDEX_BORDER_TITLE,
        rawData: {
          groupCode: item.businessData.dataset,
          resourceInterfaceId,
        },
      };

      return result;
    }

    function getTableHeader(header: Recordable[], position: Position, style: TableStyle) {
      const result = header.map((i, index) => {
        const color = item.businessData.tableConfig.headerColor || COLOR;
        const x = getColPositionX(header, index, position);
        const y = position.y + getTableTitleHeightByTablePen(item);
        const width = i.width || COL_MIN_WIDTH;
        const size = Number(style.header?.fontSize) || FONT_SIZE_MEDIUM;

        const headerItem = {
          name: 'rectangle',
          text: i.title || i.originName,
          x,
          y,
          width,
          height: getTableHeaderHeightByTablePen(item),
          color,
          hoverColor: color,
          activeColor: color,
          lineWidth: 0,
          disableAnchor: true,
          locked: LockState.DisableMove,
          background: item.businessData.tableConfig.headerBackground || '',
          fontSize: getFontSizeByScale(size, scale),
          textAlign: 'left',
          category: item.kitTypeName,
          categoryCN: '',
          textLeft: 0,
        };

        return headerItem;
      });

      return result;
    }

    function getTableBody(
      data: Recordable,
      position: Position,
      style: TableStyle,
      context: Recordable,
    ) {
      const tableHeader: Recordable[] = data.businessData.tableConfig.header;
      // const tableData: Recordable[] = data.businessData.tableConfig.data;
      const indexes: Recordable[] = data.businessData.displayTableDataInfos;
      const resourceInterfaceId = data.businessData.resourceInterfaceId;

      function getText(
        data1: { rowData: Recordable; rowIndex: number },
        data2: { hItem: Recordable; hIndex: number },
      ) {
        const { rowData, rowIndex } = data1;
        const { hItem, hIndex } = data2;
        const { isIndexValue, originName } = hItem;
        const indicator = isIndexValue ? rowData.find((i) => i.groupCode === originName) : null;
        // 没有匹配到指标项，直接返回，指标项一定要匹配到指标
        if (isIndexValue && !indicator) return null;

        indicator && (indicator.resourceInterfaceId = resourceInterfaceId);
        const color = hItem.isIndexValue
          ? getNumberColor(indicator, hItem.color)
          : hItem.color || COLOR;
        const x = getColPositionX(tableHeader, hIndex, position);

        const titleHeight = context.tableTitle.height;
        const headerHeight = context.tableHeader[0].height;
        const y =
          titleHeight + headerHeight + position.y + getTableRowHeightByTablePen(data) * rowIndex;

        const size = Number(style.body?.fontSize) || FONT_SIZE_MEDIUM;

        function getTextFn() {
          let text = hItem.isIndexValue ? indicator.expression : rowData[0][hItem.originName];

          if (indicator) {
            // JQBQ_AUTO_BAOQI_MINUTE_AVG 调节阀信号 0=手动模式 1=自动模式
            // JQBQ_XCQDFMMS_MINUTE_AVG 西侧控制模式  0=前段模式  1=中段模式  2=均值模式
            // JQBQ_DCQDFMMS_MINUTE_AVG 东侧控制模式 0=前段模式  1=中段模式  2=均值模式

            if (indicator.code === 'JQBQ_AUTO_BAOQI_MINUTE_AVG') {
              if (indicator.expression == '0.0') {
                text = '手动模式';
              } else if (indicator.expression == '1.0') {
                text = '自动模式';
              }
            } else if (indicator.code === 'JQBQ_XCQDFMMS_MINUTE_AVG') {
              if (indicator.expression == '0.0') {
                text = '前段模式';
              } else if (indicator.expression == '1.0') {
                text = '中段模式';
              } else if (indicator.expression == '2.0') {
                text = '均值模式';
              }
            } else if (indicator.code === 'JQBQ_DCQDFMMS_MINUTE_AVG') {
              if (indicator.expression == '0.0') {
                text = '前段模式';
              } else if (indicator.expression == '1.0') {
                text = '中段模式';
              } else if (indicator.expression == '2.0') {
                text = '均值模式';
              }
            } else {
            }
          }

          return text;
        }

        const result = {
          name: 'text',
          text: getTextFn(),
          color,
          hoverColor: color,
          activeColor: color,
          disableAnchor: true,
          locked: LockState.DisableMove,
          x,
          y,
          textLeft: 0,
          width: hItem.width || COL_MIN_WIDTH,
          height: getTableRowHeightByTablePen(data),
          fontSize: getFontSizeByScale(size, scale),
          fontWeight: 400,
          textAlign: 'left',
          lineWidth: 0,
          category: data.kitTypeName,
          categoryCN: hItem.isIndexValue ? TABLE_KIT_INDEX_VALUE : '',
          indexName: '',
          rawData: indicator,
        };

        if (hItem.isIndexValue) {
          result['events'] = [
            {
              action: EventAction.Emit,
              fn: null,
              name: 'enter',
              value: DIAGNOSTIC_ANALYSIS_EVENT,
              params: '报警诊断分析移入',
              where: { type: null },
            },
            {
              action: EventAction.Emit,
              fn: null,
              name: 'leave',
              value: DIAGNOSTIC_MOUSELEAVE_EVENT,
              params: '报警诊断分析移出',
              where: { type: null },
            },
          ];
        }

        return result;
      }

      function getInput(
        data1: { item: Recordable; index: number },
        data2: { hItem: Recordable; hIndex: number },
        indexes: Recordable[],
      ) {
        const { item, index } = data1;
        const { hItem, hIndex } = data2;

        const indicator = indexes.find((i) => i.code === item[hItem.originName]) as Recordable;
        const titleHeight = getTableTitleHeightByTablePen(item);
        // NOTE: 不需要title，如果不需要title，titleHeight = 0
        // titleHeight = 0;
        const headerHeight = getTableHeaderHeightByTablePen(data);
        const x = getColPositionX(tableHeader, hIndex, position);
        const y =
          titleHeight + headerHeight + position.y + getTableRowHeightByTablePen(data) * index;
        const width = (hItem.width || COL_MIN_WIDTH) - 16;
        const height = getTableHeaderHeightByTablePen(data);
        const DARK_COLOR = COLOR;
        const statusId = buildShortUUID();
        const rawData = { ...indicator, statusId };

        const input = {
          name: 'rectangle',
          width,
          height,
          disableAnchor: true,
          borderRadius: 0.05,
          input: true,
          ellipsis: true,
          text: hItem.isIndexValue && indicator ? indicator.expression : item[hItem.originName],
          // text: '',
          textAlign: 'left',
          color: INPUT_BORDER_COLOR,
          textColor: theme === 'dark' ? DARK_COLOR : LIGHT_COLOR,
          hoverTextColor: theme === 'dark' ? DARK_COLOR : LIGHT_COLOR,
          activeTextColor: theme === 'dark' ? DARK_COLOR : LIGHT_COLOR,
          textLeft: 10,
          children: [],
          lineWidth: 1,
          fontSize: 12,
          lineHeight: 1.5,
          anchors: [
            {
              id: '0',
              x: 0.5,
              y: 0,
            },
            {
              id: '1',
              x: 1,
              y: 0.5,
            },
            {
              id: '2',
              x: 0.5,
              y: 1,
            },
            {
              id: '3',
              x: 0,
              y: 0.5,
            },
          ],
          rotate: 0,
          x,
          y,
          locked: LockState.DisableMove,
          category: data.kitTypeName,
          categoryCN: TABLE_KIT_FORM_ITEM,
          indexName: '',
          rawData,
        };

        const status = indicator
          ? getInputStatus({ x: x + width + 6, y: y + 8 }, indicator.indexStatus, rawData)
          : false;

        return [input, status].filter(Boolean);
      }

      function getInputStatus(position: Position, status: DistributeStatus, rawData: Recordable) {
        const result = {
          name: status === '1' ? 'gif' : 'image',
          image: getInputStatusImg(status),
          width: 16,
          height: 16,
          visible: status !== '-1',
          x: position.x,
          y: position.y,
          disableAnchor: true,
          category: data.kitTypeName,
          categoryCN: DISTRIBUTE_STATUS,
          locked: LockState.DisableMove,
          rawData,
        };

        return result;
      }

      const rowGroup = getTableBodyRowGroup(data);

      const result = Object.keys(rowGroup).map((key, rowIndex) => {
        const rowData: Array<Object> = rowGroup[key];

        const row = tableHeader.map((hItem, hIndex) => {
          let result: Recordable | null = null;

          switch (hItem.type) {
            case 'text':
              result = getText({ rowData, rowIndex }, { hItem, hIndex }, indexes);
              break;
            case 'input':
              result = getInput({ item, index: rowIndex }, { hItem, hIndex }, indexes);
              break;
            default:
              result = getText({ item, index }, { hItem, hIndex }, indexes);
              break;
          }

          return result;
        });
        return row;
      });

      return result.flat().filter((i) => i);
    }

    async function getTableBackground(data: Recordable) {
      const result = {
        name: '表格边框',
        image: '',
        width: data.frameWidth,
        height: data.frameHeight,
        x: data.positionX,
        y: data.positionY,
        disableAnchor: true,
        isBottom: true,
        locked: LockState.DisableMove,
        category: item.kitTypeName,
        categoryCN: '',
      };

      if (data.frameType === 1) {
        const src = getCardBgSrc(data.frameBackgroundType, data.frameBackground) || tableImg;
        // const img = await getImgSize(src);
        result.image = src;
        // result.width = img.width || data.frameWidth;
        // result.height = img.height || data.frameHeight;
      }

      return item.frameType === 1 ? result : [];
    }

    try {
      // 从原始数据直接计算位置，不使用保存的数据
      const SimulationData = getPenByFlowModelIdInFlowData(
        meta2dData?.originPens,
        item.flowModelId,
      );
      if (SimulationData) {
        const frameWidth = getTableWidthByTablePenData(item);
        if (frameWidth) item.frameWidth = frameWidth;
        const frameHeight = getTableFrameHeightByTablePenData(item);
        if (frameHeight) item.frameHeight = frameHeight;
        updateKitPosition(item.framePlacement, item, SimulationData);
      }
    } catch (error) {
      console.log('error', error);
    }

    try {
      const { positionX, positionY, kitDisplayName } = item;
      const { tableConfig, resourceInterfaceId } = item.businessData;
      const { style = {}, header = [] } = tableConfig;
      const position = { x: positionX, y: positionY };
      const tableBackground = await getTableBackground(item);
      const width = getTableWidthByTablePenData(item, false);
      const tableTitle = getTableTitle(kitDisplayName, position, width, style, resourceInterfaceId);
      const tableHeader = getTableHeader(header, position, style);
      const tableBody = getTableBody(item, position, style, { tableTitle, tableHeader });

      // 存在边框，处理除了边框外的其他元素，x、y需要加上边距的偏移量
      if (isHasBorder(item)) {
        const framePadding = getKitPaddingByTablePen(item);
        const offset = { xOffset: framePadding[3], yOffset: framePadding[0] };
        processTableChildrenPenOffset(tableTitle, offset);
        processTableChildrenPenOffset(tableHeader, offset);
        processTableChildrenPenOffset(tableBody, offset);
      }
      testPen(tableTitle);
      testPen(tableHeader);
      testPen(tableBody);

      return [tableBackground, tableTitle, tableHeader, tableBody];
    } catch (err) {
      console.log('-------------> err', err);
    }
  }

  const result = await Promise.all(
    data.map(async (item) => {
      const pens = await getPens(item, scale);
      return pens;
    }),
  );

  return result.flat(3);
}

/**
 * 获取随机整数
 * @param {number} min 最大值
 * @param {number} max 最小值
 * @returns number
 */
export function radomNumber(min: number, max: number) {
  return Number.parseInt(Math.floor(Math.random() * (max - min + 1) + min) + '');
}

/**
 * 获取ECharts折线图配置项
 * @param {Array} data 原table套件数据
 * @param {number} scale 画布缩放
 * @returns result 画布的table套件数据
 */
export function getLineOption() {
  return {
    tooltip: {
      trigger: 'axis',
    },
    grid: grid,
    legend: legend,
    xAxis: lineXAxis,
    yAxis: [lineYAxis],
    series: [lineSeries],
  };
}

/**
 * 获取ECharts配置项
 * @param {String} type 图表类型
 * @returns result ECharts配置项
 */
export function getEChartOption(type: 'line' | 'bar' = 'line'): Recordable {
  const result = getBaseOption(type);

  function getBaseOption(type) {
    let option = getLineOption();

    switch (type) {
      case 'line':
        option = getLineOption();
        break;
      case 'bar':
        break;
      default:
        break;
    }

    return option;
  }

  return result;
}

/**
 * 获取Legend配置
 * @param {Array} data 指标数据
 * @param {string} key 字段名
 * @param {string} max 默认最大选中的数量
 * @param {Object} option 默认配置
 * @returns result Legend配置
 */
export function getLegend(data: Recordable[], key: string, max: number, option = legend) {
  const result = cloneDeep(option);
  result.icon = isIPCRef.value ? 'circle' : legend.icon;
  result.itemWidth = isIPCRef.value ? 16 : legend.itemWidth;
  result.itemHeight = isIPCRef.value ? 16 : legend.itemHeight;
  result.textStyle.fontSize = isIPCRef.value ? FONT_SIZE_LARGE + 2 : FONT_SIZE;
  const names = data.map((i) => i[key]);

  if (names.length > max) {
    const unchecked = names.slice(max - 1);
    unchecked.forEach((key) => {
      result.selected[key] = false;
    });
  }

  return result;
}

/**
 * 获取XAxis配置
 * @param {Array} data 指标数据
 * @param {string} path 路径
 * @param {string} key 字段名
 * @param {string} dateFormat 日期格式化
 * @param {Object} option 默认配置
 * @returns result XAxis配置
 */
export function getXAxis(
  data: Recordable[],
  path: string | string[],
  key: string,
  dateFormat = 'HH:mm',
  option = lineXAxis,
) {
  const item = get(data, path, []);

  const result = cloneDeep(option);
  if (isIPCRef.value) {
    result.axisLabel.textStyle.fontSize = FONT_SIZE_LARGE + 3;
  }
  result.data = item.map((i) => dayjs(i[key]).format(dateFormat));

  return result;
}

/**
 * 获取YAxis配置
 * @param {Array} data 指标数据
 * @param {string} iteratee 转换key
 * @param {Object} option 默认配置
 * @returns result YAxis配置
 */
export function getYAxis(
  data: Recordable[],
  iteratee: Array<any> | Function | Object | string,
  option: Recordable = lineYAxis,
) {
  const group = groupBy(data, iteratee);
  const names = Object.keys(group);

  function getOffset(index) {
    if (index < 2) {
      return 0;
    }

    if (index < 4) {
      return 65;
    }

    if (index < 6) {
      return 130;
    }

    if (index < 8) {
      return 195;
    }

    if (index < 10) {
      return 260;
    }

    return 325;
  }

  function getYAxisInterval(arr: number[][]) {
    const maxArr = arr.map((item) => {
      const itemArr = item.filter((i) => !Number.isNaN(i));
      return item.length ? Math.max(...itemArr) : 1;
    });

    const minArr = arr.map((item) => {
      const itemArr = item.filter((i) => !Number.isNaN(i));
      return item.length ? Math.min(...itemArr) : 0;
    });

    let max = Math.max(...maxArr);
    const min = Math.min(...minArr);
    if (max === min) {
      max = max + 1;
    }

    const range = max - min;
    const proportion = 0.1;
    let offset = range * proportion;

    const minOffset = 0.005;
    if (offset < minOffset) {
      offset = minOffset;
    }

    const result = {
      max: max + offset > 10 ? (max + offset).toFixed(0) : (max + offset).toFixed(3),
      min: max + offset > 10 ? (min - offset).toFixed(0) : (min - offset).toFixed(3),
    };

    if (result.max === result.min) {
      result.max = (+result.max * 1.1).toFixed(offset > 10 ? 0 : 3);
      result.min = (+result.min * 0.9).toFixed(offset > 10 ? 0 : 3);
    }

    if (Number(result.min) < 0) {
      result.min = '0';
    }

    // 上述代码在整数相同时Y轴最大值会计算错误，这里做修正
    if (Number(result.max) < max) {
      result.max = (max + 1).toFixed(0);
    }

    return result;
  }

  const result = names.map((name, index) => {
    const item = cloneDeep(option);
    if (isIPCRef.value) {
      item.axisLabel.textStyle.fontSize = FONT_SIZE_LARGE + 3;
    }
    const dataArr = group[name].map((i) => {
      const numbers = i.data.map((j) => parseFloat(j.value));
      return numbers;
    });
    item.name = name.includes('单位') ? name : name ? `单位(${name})` : '';
    item.min = getYAxisInterval(dataArr).min || 0;
    item.max = getYAxisInterval(dataArr).max || 1;
    item.position = index % 2 ? 'right' : 'left';
    const power = Math.pow(10, 6);
    item.interval = Math.round(((item.max - item.min) / 5) * power) / power;
    item.offset = getOffset(index);
    item.axisLine = {
      show: true,
      lineStyle: {
        color: option.axisLine.lineStyle.color || '#fff',
      },
    };

    return item;
  });

  return result;
}

/**
 * 获取Series配置
 * @param {Array} data 指标数据
 * @param {Object} indicator field-指标数据字段名 key-指标值字段名
 * @param {Object} yAxis names-y轴名称数组 key-指标y轴字段名
 * @param {string} seriesName series名称字段名
 * @param {Object} option 默认配置
 * @returns result Series配置
 */
export function getSeries(
  data: Recordable[],
  indicator: { dataField: string; valueField: string },
  yAxis: { names: string[]; yNameField: string },
  seriesName: string,
  option = lineSeries,
) {
  const values = data.map((i) => i[indicator.dataField]);
  const dataArr = values.map((i) => i.map((j) => j[indicator.valueField]));

  const result = data.map((item, i) => {
    const _series_ = cloneDeep(option);
    _series_.data = dataArr[i];
    _series_.name = item[seriesName];

    const yName = item[yAxis.yNameField];

    const index = yAxis.names.findIndex((i) => i.includes(yName));
    if (index > -1) {
      _series_.yAxisIndex = index;
    }

    return _series_;
  });

  return result;
}

/**
 * 获取画布的chart套件数据
 * @param {Array} data 原chart套件数据
 * @param {number} scale 画布缩放
 * @param {Meta2dData} meta2dData Meta2dData
 * @returns result 画布的chart套件数据
 */
export async function getChartKitMeta2dData(
  data: Recordable[],
  scale: number,
  meta2dData: Meta2dData | undefined,
) {
  async function getPens(item: Recordable, scale: number) {
    const { option: echartsData } = item.businessData?.echarts || {};
    async function getChartBackground(data: Recordable) {
      const result = {
        name: '图表边框',
        image: '',
        width: 0,
        height: 0,
        x: data.positionX,
        y: data.positionY,
        disableAnchor: true,
        isBottom: true,
        locked: LockState.DisableMove,
        category: item.kitTypeName,
        categoryCN: '',
      };

      if (data.frameType === 1) {
        const background = data.frameBackground || lineChartImg;
        const img = await getImgSize(background);
        result.image = background;
        result.width = img.width;
        result.height = img.height;
      }

      return result || null;
    }

    function getChartTitle(name: string, position: Position) {
      const size = isIPCRef.value ? FONT_SIZE_LARGE + 3 : FONT_SIZE_LARGE;
      const result = {
        name: 'text',
        text: name,
        x: position.x + 13,
        y: position.y + 8,
        width: 200,
        height: 32,
        color: COLOR,
        hoverColor: TITLE_COLOR,
        activeColor: TITLE_COLOR,
        disableAnchor: true,
        locked: LockState.DisableMove,
        fontSize: getFontSizeByScale(size, scale),
        textAlign: 'left',
        category: item.kitTypeName,
        categoryCN: '',
      };

      return result;
    }

    function getFormItem(type: 'Select' | 'DatePicker', position: Position, borderWidth: number) {
      const selectPen = {
        x: position.x + borderWidth - 120 - 20,
        y: positionY + 30,
        height: 28,
        width: 120,
        disableAnchor: true,
        name: 'rectangle',
        borderRadius: 0.05,
        ellipsis: true,
        text: '选项3',
        // text: '进24小时',
        textAlign: 'left',
        input: true,
        color: '#D9D9D9FF',
        textColor: '#000000FF',
        hoverTextColor: '#000000FF',
        activeTextColor: '#000000FF',
        // color: '#2EBDFF',
        // textColor: COLOR,
        // hoverTextColor: COLOR,
        // activeTextColor: COLOR,
        textLeft: 10,
        dropdownList: [
          {
            text: '选项1',
          },
          {
            text: '选项2',
          },
          {
            text: '选项3',
          },
        ],
        // dropdownList: [
        //      {
        //        text: '进24小时',
        //      },
        //      {
        //       text: '进12小时',
        //    },
        //      {
        //       text: '进6小时',
        //      },
        //    ],
        form: [
          {
            key: 'text',
            name: '选择项',
            type: 'text',
          },
        ],
        children: [],
        lineWidth: 1,
        fontSize: FONT_SIZE,
        lineHeight: 1.5,
        anchors: [
          {
            id: '0',
            penId: '2189b371',
            x: 0.5,
            y: 0,
          },
          {
            id: '1',
            penId: '2189b371',
            x: 1,
            y: 0.5,
          },
          {
            id: '2',
            penId: '2189b371',
            x: 0.5,
            y: 1,
          },
          {
            id: '3',
            penId: '2189b371',
            x: 0,
            y: 0.5,
          },
        ],
        rotate: 0,
        events: [],
        locked: LockState.DisableMove,
        category: item.kitTypeName,
        categoryCN: '',
      };
      const datePickerPen = {};

      let result: Recordable = selectPen;
      switch (type) {
        case 'Select':
          result = selectPen;
          break;
        case 'DatePicker':
          result = datePickerPen;
          break;
        default:
          break;
      }

      return result;
    }

    async function getChart(data: Recordable) {
      async function getIndexData() {
        console.log('------------------------------------------ FACTORY_KEY');
        // TODO 日期参数由表单项拿
        const ls = createLocalStorage();
        const tenantId = ls.get(TENANTID_KEY) || '';
        const factoryId = getFactoryId() || '';
        const params = {
          factoryId,
          tenantId,
          startDateTime: dayjs().format('YYYY-MM-DD 00:00:00'),
          endDateTime: dayjs().format('YYYY-MM-DD 23:59:59'),
          indexCodes: data.businessData.indexCodes.map((i) => i.originName).join(','),
          groupId: data.businessData.groupId,
          resourceInterfaceId: data.businessData.resourceInterfaceId,
        };
        const result = await getIndexListApi(params);
        return result;
      }
      const indexes = await getIndexData();

      function _getOption_(indexes: Recordable[]) {
        const data = isIPCRef.value ? indexes.slice(0, 4) : indexes;
        const legendMax = isIPCRef.value ? 4 : 7;
        const _legend_ = getLegend(data, 'indexName', legendMax, echartsData.legend);
        const _xAxis_ = getXAxis(
          data,
          ['0', 'data'],
          'collectDateTime',
          'HH:mm',
          echartsData.xAxis,
        );
        const _yAxis_ = getYAxis(data, 'unitName', echartsData.yAxis[0]);
        const _series_ = getSeries(
          data,
          { dataField: 'data', valueField: 'value' },
          { names: _yAxis_.map((i) => i.name), yNameField: 'unitName' },
          'indexName',
        );

        function getDistance(yAxis: Recordable[]) {
          const length = yAxis.length;

          return 25 + 15 * length;
        }

        const option = getEChartOption();
        option.legend = _legend_;
        option.xAxis = _xAxis_;
        option.yAxis = _yAxis_;
        option.series = _series_;

        const distance = getDistance(option.yAxis);
        option.grid = {
          left: distance,
          right: distance,
          top: option.grid.top || grid.top,
          bottom: option.grid.bottom || grid.bottom,
          containLabel: true,
        };

        if (isIPCRef.value) {
          option.grid.left = 45;
          option.grid.bottom = 35;
        }
        console.log('option', option);

        return option;
      }

      const option = _getOption_(indexes);

      // const WIDTH = 40;
      // const HEIGHT = 80;
      const result = {
        name: 'echarts',
        // width: background.width - WIDTH,
        // height: background.height - HEIGHT,
        // x: data.positionX + WIDTH / 2,
        // y: data.positionY + HEIGHT - 10,
        width: Number(data.businessData.width) || CHART_WIDTH,
        height: Number(data.businessData.height) || CHART_HEIGHT,
        x: data.positionX,
        y: data.positionY,
        externElement: true,
        disableAnchor: true,
        category: data.kitTypeName,
        categoryCN: '',
        locked: LockState.DisableMove,
        echarts: {
          option: {},
        },
        // zIndex: 4,
      };
      // 当y轴数组为空，echarts会报错，导致le5le渲染错误
      if (!option.yAxis || option.yAxis.length < 1) {
        delete option.yAxis;
        delete option.series;
        delete option.xAxis;
      }

      result.echarts.option = option;

      return result;
    }

    getPositionByScale(item, meta2dData);
    const { positionX, positionY, kitDisplayName } = item;
    const position = { x: positionX, y: positionY };
    const chartBackground = await getChartBackground(item);

    const chartTitle = getChartTitle(kitDisplayName, position);

    const background = { width: chartBackground.width, height: chartBackground.height };
    const chart = await getChart(item, background);

    const formItem = getFormItem(item.businessData.formItemType, position, background.width);
    console.log('图表-背景-标题-表单', chartBackground, chartTitle, formItem);
    return [chart];
  }
  let res;
  try {
    res = await Promise.all(
      data.map(async (item) => {
        const pens = await getPens(item, scale);
        return pens;
      }),
    );
  } catch (err) {
    console.log('--------------> chart err', err);
  }

  return res.flat();
}

/**
 * 获取画布的视频套件数据
 * @param {Array} data 原视频套件数据
 * @param {Meta2dData} meta2dData Meta2dData
 * @returns result 画布的视频套件数据
 */
export function getVideoKitMeta2dData(data: Recordable[], meta2dData: Meta2dData | undefined) {
  function getPens(item) {
    item.frameWidth = item.frameWidth || VIDEO_IMG_WIDTH;
    item.frameHeight = item.frameHeight || VIDEO_IMG_HEIGHT;

    const SimulationData = getPenByFlowModelIdInFlowData(meta2dData?.originPens, item.flowModelId);
    if (!SimulationData) throw new Error('未找到流程模型数据');
    if (!meta2dData) throw new Error('没有原始数据');
    const { positionX: x, positionY: y } =
      getPositionBySimulation(item.framePlacement, item, SimulationData) || {};

    const result = {
      name: '视频',
      image: item.image || videoImg,
      x,
      y,
      width: item.frameWidth,
      height: item.frameHeight,
      kitId: item.id,
      kitDisplayName: item.kitDisplayName,
      detailModelId: item.detailModelId,
      category: item.kitTypeName,
      locked: LockState.DisableMove,
      disableAnchor: true,
    };

    result['events'] = [
      {
        action: EventAction.Emit,
        fn: null,
        name: 'enter',
        value: DIAGNOSTIC_ANALYSIS_EVENT,
        params: '报警诊断分析移入',
        where: { type: null },
      },
      {
        action: EventAction.Emit,
        fn: null,
        name: 'leave',
        value: DIAGNOSTIC_MOUSELEAVE_EVENT,
        params: '报警诊断分析移出',
        where: { type: null },
      },
    ];

    return result;
  }

  const result = data.map((item) => {
    const kit = item.businessData;
    const pens = getPens(item, kit);

    return pens;
  });

  return result.flat(2);
}

/**
 * 获取画布的小元件数据
 * @param {Array} data 原小元件数据
 * @returns result 画布的小元件数据
 */
export function getItemKitMeta2dData(data: Recordable[], scale: number) {
  function getPens(item: Recordable) {
    const valueWidth = 70;
    const valueHeight = 28;
    const lineHeight = 30;
    const margin = 30;
    const modelWidth = item.businessData.itemWidth;
    const modelWidthHalf = modelWidth / 2;

    function getValue(item) {
      const text = Number(item.businessData.productDataInfos[0]?.dataValue || 0);
      const result = {
        name: 'rectangle',
        text: `${text}%`,
        x: item.positionX + modelWidthHalf - valueWidth / 2 + 8,
        y: item.positionY - valueHeight - lineHeight - margin,
        width: valueWidth,
        height: valueHeight,
        background: '#BBDCFF',
        textColor: '#147CEA',
        color: '#0F70DF',
        hoverColor: '#0F70DF',
        activeColor: '#0F70DF',
        locked: LockState.DisableMove,
        disableAnchor: true,
        borderRadius: 0.1,
        fontSize: getFontSizeByScale(FONT_SIZE, scale),
        textAlign: 'center',
        category: item.kitTypeName,
        categoryCN: '',
      };

      return result;
    }

    function getLine(item) {
      const result = {
        name: 'line',
        lineName: 'line',
        x: item.positionX + modelWidthHalf + 8,
        y: item.positionY - lineHeight - margin - 5,
        type: 1,
        lineWidth: 1,
        length: lineHeight + valueHeight,
        width: 0,
        height: lineHeight + valueHeight,
        fontSize: FONT_SIZE,
        lineHeight: 1.5,
        color: '#0F70DF',
        hoverColor: '#0F70DF',
        activeColor: '#0F70DF',
        anchors: [
          { x: 0.1, y: 0.1 },
          { x: 0.1, y: 0.5 },
          { x: 1, y: 1 },
        ],
        locked: LockState.DisableMove,
        disableAnchor: true,
        category: item.kitTypeName,
        categoryCN: '',
      };

      return result;
    }

    const value = getValue(item);
    const line = getLine(item);
    return [value, line];
  }

  const itemOtherPens = data.map((item) => {
    const p = item.businessData.itemType === ITEM_TYPES[0].value ? [] : getPens(item);
    return p;
  });

  function isEmptyStr(s) {
    if (s == undefined || s == null || s == '') {
      return true;
    }
    return false;
  }

  const itemImgPens = data
    .map((item) => {
      const data = item.businessData;
      const { semaphoreImageList, scriptResult } = data;
      const image = isEmptyStr(scriptResult) ? null : semaphoreImageList[scriptResult]?.imagePath;
      return {
        id: item.flowModelId,
        image: addResourcePrefix(image),
      };
    })
    .filter((i) => i.image);

  return { itemImgPens: itemImgPens, itemOtherPens: itemOtherPens.flat() };
}

/**
 * 获取画布的液位套件数据
 * @param {Array} data 液位套件数据
 * @returns result 画布的小元件数据
 */
export function getLiquidLevelKitMeta2dData(data: Recordable[]) {
  const LiquidLevelPens = data.map((item) => {
    const isGif = (item.businessData.image || '').endsWith('.gif');
    const image = item.businessData.image;
    const modelWidth = Number(item.businessData.modelWidth);
    const modelHeight = item.businessData.modelHeight;
    const scriptResult = Number(item.businessData.scriptResult) || 0.1;
    const max = item.businessData.max || 1;

    const percentage = scriptResult / max;
    const height = modelHeight * percentage;

    return {
      name: isGif ? 'gif' : 'image',
      image: addResourcePrefix(image),
      width: modelWidth,
      height: height,
      visible: true,
      x: item.positionX,
      y: item.positionY,
      disableAnchor: true,
      category: item.kitTypeName,
      categoryCN: item.kitTypeName,
      locked: LockState.DisableMove,
      rawData: {
        code: item.businessData.indicator,
      },
      // updateParams: {
      //   name: isGif ? 'gif' : 'image',
      //   width: modelWidth,
      //   height: modelHeight * percentage,
      // },
    };
  });

  return LiquidLevelPens;
}

/**
 * 计算 background 左上角位置
 * @param e
 * @param data
 * @param activePen
 * @returns
 */
export function getPositionBySimulation(e: number, data: Recordable, activePen: Recordable) {
  const modelX = activePen.x;
  const modelY = activePen.y;
  const modelWidth = activePen.width;
  const modelHeight = activePen.height;
  const modelWidthHalf = activePen.width / 2;

  const frameWidth = data.frameWidth;
  const frameHeight = data.frameHeight;

  const frameWidthHalf = frameWidth / 2;
  const margin = 8;
  let positionX;
  let positionY;

  switch (e) {
    case PLACEMEN_ENUM.TOP:
      // 上
      positionX = modelX + modelWidthHalf - frameWidthHalf;
      positionY = modelY - frameHeight - margin;
      break;
    case PLACEMEN_ENUM.BOTTOM:
      // 下
      positionX = modelX + modelWidthHalf - frameWidthHalf;
      positionY = modelY + modelHeight + margin;
      break;
    case PLACEMEN_ENUM.LEFT:
      // 左
      positionX = modelX - frameWidth - margin;
      positionY = modelY;
      break;
    case PLACEMEN_ENUM.RIGHT:
      // 右
      positionX = modelX + modelWidth + margin;
      positionY = modelY;
      break;
    default:
      break;
  }

  return positionX === undefined
    ? undefined
    : {
        positionX,
        positionY,
      };
}

/**
 * 更新位置(无边框)
 * @param {number} e 外框方位
 * @param {Object} data 源数据
 * @param {Object} activePen 选中的图元
 */
export function updatePositionNoBorder(e: number, data: Recordable, activePen: Recordable) {
  return updateKitPosition(e, data, activePen);
}

export function isHasBorder(data: Recordable) {
  return data.frameType === FRAMETYPE_ENUM.HAS_BORDER;
}

/**
 * TODO: 待废弃，统一用updateKitPosition函数
 * 更新位置(有边框)
 * @param {number} e 外框方位
 * @param {Object} data 源数据
 * @param {Object} activePen 选中的图元
 */
export function updatePositionHasBorder(e: number, data: Recordable, activePen: Recordable) {
  return updateKitPosition(e, data, activePen);
}

/**
 * 更新套件位置
 * @param {number} e 外框方位
 * @param {Object} data 源数据
 * @param {Object} activePen 选中的图元
 */
export function updateKitPosition(e: number, data: Recordable, activePen: Recordable) {
  const position = getPositionBySimulation(e, data, activePen);
  if (!position) return;
  data.positionX = position.positionX;
  data.positionY = position.positionY;
}
// 处理小组件移入移出事件
export function processHoverCursor(item) {
  if (item.ModelType === 'Equipment') {
    const events = [
      {
        action: EventAction.JS,
        fn: null,
        name: 'enter',
        value: `if (!pen) return
                if (pen.showDetail || pen._pointer) 
                  meta2d.setOptions({ hoverCursor: 'pointer' });`,
        params: '报警诊断分析移入',
        where: { type: null },
      },
      {
        action: EventAction.JS,
        fn: null,
        name: 'leave',
        value: `meta2d.setOptions({ hoverCursor: 'default' });`,
        params: '报警诊断分析移出',
        where: { type: null },
      },
    ];
    if (item.events) {
      item.events.push(...events);
    } else {
      item.events = events;
    }
  }
}

export function getPenByFlowModelIdInFlowData(flowData, flowModelId: string) {
  return flowData.find((item) => item.id === flowModelId);
}

export function getTableWidthByTablePenData(data: Recordable, havePadding = true): number {
  if (!data) return 0;
  const { tableConfig } = data?.businessData || {};
  const framePadding = getKitPaddingByTablePen(data);
  const cols = tableConfig?.header || [];
  if (!cols?.length) return 0;
  let frameWidth = getTotalInArray(cols, 'width');
  if (isHasBorder(data) && havePadding) {
    frameWidth += Number(framePadding[1]) + Number(framePadding[3]);
  }
  return frameWidth;
}

export function getWidthByProductPenData(data: Recordable, havePadding = true) {
  let cols = data?.businessData?.kitConfig?.column?.width;
  const showArr = data?.businessData?.kitConfig?.column?.show;
  if (Array.isArray(showArr)) {
    cols = cols.filter((item, index) => showArr[index]);
  }
  if (!cols) return 0;
  const framePadding = getKitPaddingByTablePen(data);
  if (!cols?.length) return 0;
  let frameWidth = getTotalInArray(cols);
  if (isHasBorder(data) && havePadding) {
    frameWidth += Number(framePadding[1]) + Number(framePadding[3]);
  }
  return frameWidth;
}

export function getTableFrameHeightByTablePenData(data: Recordable): number {
  if (!data) return 0;
  const titleHeight = getTableTitleHeightByTablePen(data);
  const headerHeight = getTableHeaderHeightByTablePen(data);
  const bodyHeight = getTableRowHeightByTablePen(data) * (getTableBodyRowLength(data) || 0);
  let frameHeight = titleHeight + headerHeight + bodyHeight;
  if (isHasBorder(data)) {
    const framePadding = getKitPaddingByTablePen(data);
    frameHeight += framePadding[0] + framePadding[2];
  }
  return frameHeight;
}

// 获取tablebody行分组数据，根据displayTableDataInfos计算，使用displayName作为key分组
function getTableBodyRowGroup(data) {
  const displayTableDataInfos = data?.businessData?.displayTableDataInfos;
  return Object.groupBy(displayTableDataInfos, (item) => item.displayName);
}
// 获取tablebody行数，根据displayTableDataInfos计算，使用displayName作为key分组
function getTableBodyRowLength(data) {
  return Object.keys(getTableBodyRowGroup(data)).length;
}

export function getFrameHeightByProductPenData(data: Recordable): number {
  if (!data) return 0;
  const titleHeight = isHaveDisplayTitleInProductPen(data)
    ? getTableTitleHeightByTablePen(data)
    : 0;
  const bodyHeight =
    getRowHeightByProductPen(data) * (data?.businessData?.displayProductDataInfos?.length || 0);
  let frameHeight = titleHeight + bodyHeight;
  if (isHasBorder(data)) {
    const framePadding = getKitPaddingByTablePen(data);
    frameHeight += framePadding[0] + framePadding[2];
  }
  return frameHeight;
}

export function getRowHeightByProductPen(data: Recordable): number {
  return data?.businessData?.kitConfig?.column?.height || 32;
}
export function getTitleHeightByProductPen(): number {
  return 52;
}

export function getTableTitleHeightByTablePen(data: Recordable): number {
  return isHaveDisplayTitleInTablePen(data) ? TABLE_HEADER_MIN_HEIGHT : 0;
}
export function isHaveDisplayTitleInProductPen(data: Recordable): boolean {
  return Boolean(getTableDisplayTitleByTablePen(data)) && isHasBorder(data);
}
export function isHaveDisplayTitleInTablePen(data: Recordable): boolean {
  return Boolean(getTableDisplayTitleByTablePen(data));
}

export function getTableHeaderHeightByTablePen(): number {
  return COL_MIN_HEIGHT;
}
export function getTableRowHeightByTablePen(): number {
  return COL_MIN_HEIGHT + 6;
}

export function getKitPaddingByTablePen(data: Recordable) {
  return getFramePadding(data?.framePadding);
}

export function getFramePadding(framePadding) {
  let res = framePadding;
  if (typeof framePadding === 'string') res = JSON.parse(framePadding);
  if (!Array.isArray(res)) return FRAME_PADDING;
  if (res.length < 4) return FRAME_PADDING;
  return res;
}

export function isAvailableFramePadding(framePadding) {
  let res = framePadding;
  if (typeof framePadding === 'string') res = JSON.parse(framePadding);
  if (!Array.isArray(res)) return false;
  if (res.length < 4) return false;
  return true;
}

export function getTableDisplayTitleByTablePen(data: Recordable): number {
  return data?.kitDisplayName;
}

// 计算table有背景图片时的偏移量
export function processTableChildrenPenOffset(pen, offset) {
  const { xOffset, yOffset } = offset;
  function processFn(obj) {
    obj.x += xOffset;
    obj.y += yOffset;
    // obj.name = 'rectangle';
    // obj.background = 'rgba(47, 212, 101, 0.5)';
  }
  if (!pen) return pen;
  if (Array.isArray(pen)) {
    pen.forEach((item) => processFn(item));
  }
  if (typeof pen === 'object') processFn(pen);
  return pen;
}

/**
 * 获取数组的和
 * @param {Array} arr 数组
 * @param {String | undefined} key 数组总的key
 * @returns {Number} 数组中所有数字的和
 */
function getTotalInArray(arr: any[], key?: string | undefined): number {
  if (!Array.isArray(arr) || arr.length < 1) return 0;
  key && (arr = arr.map((item) => item[key]));
  return arr.reduce((pre, cur) => pre + cur, 0);
}

function getRenderRectByPenRect(data: Recordable, origin, scale) {
  return {
    x: scale * data.x + origin.x,
    y: scale * data.y + origin.y,
    width: scale * data.width,
    height: scale * data.height,
  };
}
export function testPen() {}
// export function testPen(pen) {
//   function processFn(obj) {
//     obj.name = 'rectangle';
//     obj.background = 'rgba(47, 212, 101, 0.5)';
//     obj.lineWidth = 0;
//   }
//   if (Array.isArray(pen)) {
//     pen.forEach((i) => processFn(i));
//   } else if (typeof pen === 'object') {
//     processFn(pen);
//   }
// }

// 根据x,y,boxWidth,boxHeight,和 penWidth、penHeight计算出pen的x,y
function getPenPositionByBoxPosition(boxPosition, pen, textAlign = 'center') {
  const { x: boxX, y: boxY, width: boxWidth, height: boxHeight } = boxPosition;
  const { width: penWidth, height: penHeight } = pen;

  let x = boxX;
  if (textAlign === 'left') {
    x = boxX;
  } else if (textAlign === 'right') {
    x = boxX + boxWidth - penWidth;
  } else if (textAlign === 'center') {
    x = boxX + (boxWidth - penWidth) / 2;
  }
  const y = boxY + (boxHeight - penHeight) / 2;
  return { x, y, width: penWidth, height: penHeight };
}
/**
 * 对设置了数据套件的pen进行更新
 * @param {Array} item pen原数据
 * @param {Array} commonKits 数据套件数据
 */
export function updateByCommonKit(item, commonKits, mode) {
  if (!commonKits?.length || mode == 'edit') return;
  const currentKit = commonKits.find((v) => v.flowModelId == item.id);
  if (!currentKit || currentKit.kitTypeName != COMMON_KIT) return;
  let styleObj: Indexable = {};
  try {
    const ret = parseJson(currentKit.businessData.scriptResult || '{}');

    styleObj = isObject(ret) ? ret : {};
  } catch (e) {}
  Object.assign(item, styleObj);
  if (!styleObj?.disabled) styleObj.disabled = false;
  try {
    if (meta2d) {
      switchPipelineFlowByCommonKit(item);
      meta2d.setValue({ id: item.id, ...styleObj });
    }
  } catch (e) {}
}
// 控制管线流动
const pipelineFlowStatusTemp = {};
const switchPipelineFlowByCommonKit = (pen) => {
  setTimeout(() => {
    try {
      const { pipelineFlow, pipelineForm } = pen;
      if (pipelineForm?.ids?.length && typeof pipelineFlow == 'boolean' && meta2d) {
        const ids = pipelineForm.ids;
        const pens = meta2d.data().pens;
        const lines = pens.filter((i) => ids.includes(i.id));
        lines.forEach((line) => {
          const autoPlay = pipelineFlow ? !line.autoPlay : line.autoPlay;
          meta2d?.setValue({
            id: line.id,
            autoPlay,
            keepAnimateState: pipelineFlow,
          });
          if (pipelineFlow) {
            if (!pipelineFlowStatusTemp[line.id]) meta2d?.startAnimate(line.id);
          } else {
            if (pipelineFlowStatusTemp[line.id]) meta2d?.stopAnimate(line.id);
          }
          pipelineFlowStatusTemp[line.id] = pipelineFlow;
        });
        meta2d?.setValue({
          id: pen.id,
          _playback: pipelineFlow,
        });
      }
    } catch (e) {}
  }, 0);
};

// function addCursorPointerEvent(pen) {
//   if (!pen) return;
//   if (!Array.isArray(pen.events)) pen.events = [];
//   pen.events.push(...getCursorPointerEvent());
// }
function getCursorPointerEvent() {
  return [getCursorPointerEnterEvent(), getCursorPointerLeaveEvent()];
}

function getCursorPointerEnterEvent() {
  return {
    action: EventAction.Emit,
    fn: null,
    name: 'enter',
    value: DIAGNOSTIC_ANALYSIS_EVENT,
    where: { type: null },
  };
}
function getCursorPointerLeaveEvent() {
  return {
    action: EventAction.Emit,
    fn: null,
    name: 'leave',
    value: DIAGNOSTIC_MOUSELEAVE_EVENT,
  };
}
// function getControlSwitchEvent() {
//   return {
//     action: EventAction.Emit,
//     fn: null,
//     name: 'click',
//     value: ,
//   };
// }
