<template>
  <CockpitCardBox title="碳中和目标">
    <template #content>
      <div class="carbon-content">
        <div class="target-box">
          <div class="target-header">
            <div class="title">本年度减碳目标</div>
            <div class="value-unit">
              <div class="value"
                >{{
                  processData.value === '' || processData.value === null
                    ? '-'
                    : formatNumber(Number(processData.value), processData.digit).num
                }}
                <span class="level">{{
                  formatNumber(Number(processData.value), processData.digit).level
                }}</span></div
              >
              <div class="unit" v-if="processData.value !== '' && processData.value !== null">{{
                processData.unit
              }}</div>
              <div
                class="proportion"
                v-if="
                  processData.value !== '' &&
                  processData.value !== null &&
                  processData.maxVal !== null
                "
                >({{
                  Number((Number(processData.value) / Number(processData.maxVal)) * 100).toFixed(0)
                }}%)</div
              >
            </div>
          </div>
          <div class="target-process">
            <div class="process-bar">
              <div
                class="bar-inner"
                :style="{
                  width:
                    !processData.value || !processData.maxVal
                      ? '0'
                      : `${Number(
                          (Number(processData.value) / Number(processData.maxVal)) * 100,
                        )}%`,
                }"
              ></div>
            </div>
          </div>
        </div>
        <div class="chart-box">
          <div ref="chartRef" class="chart w-full h-full"></div>
        </div>
      </div>
    </template>
  </CockpitCardBox>
</template>

<script lang="ts" setup>
  import { ref, PropType, watch } from 'vue';
  import dayjs from 'dayjs';
  import { CockpitCardBox } from './CockpitCard';
  import { getScaleValByClientWidth } from '../data';
  import { useECharts } from '/@/hooks/web/useECharts';
  import type { AerationData, ChartData } from '../type';
  import { formatNumber } from '/@aoa/utils/number';

  const props = defineProps({
    processData: {
      type: Object as PropType<AerationData>,
      default: () => {},
    },
    data: {
      type: Array as PropType<ChartData[]>,
      default: () => [],
    },
    first: Boolean,
  });

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef as any);
  const colorList = ['rgba(11, 167, 137, 0.72)', 'rgba(117, 255, 229, 0.72)'];

  const getXAxisData = (data: ChartData[]) => {
    if (data.length === 0) return [];
    return data[0].data?.map((item) => item.collectDateTime);
  };

  const getMax = (data: ChartData[]) => {
    const dataArr = data
      .map((item) => item.data)
      .flat()
      .map((item) => item.value)
      .filter((item) => item !== null) as number[];
    return dataArr.length <= 0 ? 500 : Math.max(...dataArr);
  };

  const getMin = (data: ChartData[]) => {
    console.log(data);
    // const dataArr = data
    //   .map((item) => item.data)
    //   .flat()
    //   .map((item) => item.value)
    //   .filter((item) => item !== null);
    // return dataArr.length <= 0 ? 0 : Math.min(...dataArr);
    return 0;
  };

  const getValueFormatter = (value, digit = 2) => {
    return Number(value).toFixed(digit);
  };

  const getSeriesData = (data: ChartData[]) => {
    const setiseData = data.map((item) => {
      return [
        {
          name: item.name,
          type: 'bar',
          barMaxWidth: 16,
          data: item.data?.map((item) => item.value),
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: colorList[0],
                },
                {
                  offset: 0.5,
                  color: colorList[1],
                },
                {
                  offset: 1,
                  color: colorList[0],
                },
              ],
              global: false,
            },
          },
        },
        {
          name: '',
          type: 'pictorialBar',
          legendHoverLink: false,
          symbolSize: ['100%', 4],
          symbolOffset: [0, -3],
          barMaxWidth: 16,
          symbolPosition: 'end',
          barGap: '-100%',
          z: 12,
          color: 'rgba(151, 252, 233, 1)',
          data: item.data?.map((item) => ({
            value: item.value,
            symbolSize: item.value ? ['100%', 4] : [0, 0],
          })),
          tooltip: {
            show: false,
          },
        },
      ];
    });
    return setiseData.flat();
  };

  const setChart = () => {
    const { data } = props;

    let max = getMax(data);
    let min = getMin(data);

    if (max === 0) {
      max = 500;
    } else {
      max = Math.ceil(max / 500) * 500;
    }
    const options = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
        appendToBody: true,
        formatter: (params) => {
          return `<span style="font-size: 0.73vw; line-height: 1.5;">${
            Number(dayjs(params[0].axisValue).format('MM')) + '月'
          }</span></br>
          ${params
            .map((item, index) => {
              return `
            ${item.marker}&nbsp;<span style="font-size: 0.73vw">${
                item.seriesName
              }</span>&nbsp;<span >${
                item.value !== '' && item.value !== null && item.value !== undefined
                  ? `<span style="font-weight: 600;font-size: 0.73vw">${getValueFormatter(
                      item.value,
                      data[index].digit,
                    )} </span>&nbsp;<span style="font-weight: 600;font-size: 0.73vw">${
                      data[index]?.unit
                    }</span>`
                  : '-'
              }</span>&nbsp;
            `;
            })
            .join('</br>')}
          `;
        },
        // valueFormatter: (value) => {
        //   return value !== '' && value !== null && value !== undefined
        //     ? `${getValueFormatter(value, data[0].digit)} ${data[0]?.unit}`
        //     : '-';
        // },
      },
      legend: {
        show: false,
      },
      grid: {
        left: 8,
        right: 8,
        bottom: 0,
        top: getScaleValByClientWidth(40),
        containLabel: true,
        show: true,
        backgroundColor: 'rgba(2, 53, 32, 0.40)',
        borderWidth: 0,
      },
      xAxis: {
        type: 'category',
        data: getXAxisData(data),
        boundaryGap: true,
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          padding: [0, 5, 0, 0],
          formatter: (value) => {
            return Number(dayjs(value).format('MM')) + '月';
          },
        },
        axisTick: {
          show: false,
        },
        axisPointer: {
          show: true,
          type: 'line',
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            type: [5, 10],
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            shadowColor: 'rgba(178, 255, 241, 1)',
            shadowOffsetY: -2,
            shadowOffsetX: 0,
            shadowBlur: 5,
            width: 1,
          },
        },
      },
      yAxis: {
        type: 'value',
        name: data[0]?.unit ? `碳排量(${data[0].unit})` : '',
        max,
        min,
        interval: (max - min) / 5,
        nameTextStyle: {
          fontSize: getScaleValByClientWidth(13),
          color: '#fff',
          align: 'left',
          padding: [0, 0, 0, getScaleValByClientWidth(-38)],
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          formatter: (value) => getValueFormatter(value, data[0]?.digit),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A1D0C4',
            type: [5, 10],
            offset: 5,
          },
        },
      },
      series: getSeriesData(data),
    };

    setOptions(options as any);
  };

  watch(
    () => props.first,
    () => {
      setChart();
    },
    { deep: true, immediate: true },
  );
</script>
<style lang="less" scoped>
  .carbon-content {
    padding: 8px 0;
    height: 100%;
    display: flex;
    flex-direction: column;

    .target-box {
      width: calc(100% - 16px);
      margin-left: 8px;

      .target-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          font-size: 14px;
          line-height: 1;
        }

        .value-unit {
          display: flex;
          align-items: center;

          .value {
            font-family: Alimama ShuHeiTi;
            font-weight: 500;
            font-size: 14;
            line-height: 1;
          }

          .unit {
            padding-left: 4px;
            font-size: 13px;
            line-height: 1;
          }

          .proportion {
            padding-left: 4px;
            font-family: Alimama ShuHeiTi;
            font-weight: 500;
            font-size: 14px;
            line-height: 1;
          }
        }
      }

      .target-process {
        margin-top: 5px;
        width: 100%;

        .process-bar {
          position: relative;
          height: 16px;
          width: 100%;
          border-radius: 4px;
          background: linear-gradient(360deg, rgba(31, 195, 164, 0.5), rgba(31, 195, 164, 0));
          overflow: hidden;

          &:after {
            position: absolute;
            content: '';
            left: 1px;
            top: 0;
            height: calc(100% - 2px);
            width: calc(100% - 2px);
            background-color: rgba(0, 106, 86, 1);
            border-radius: 4px;
          }

          .bar-inner {
            position: absolute;
            left: 0;
            top: 0;
            height: 16px;
            width: 50%;
            border-radius: 4px;
            border-image: linear-gradient(360deg, rgba(31, 195, 164, 0.5), rgba(31, 195, 164, 0));
            overflow: hidden;
            z-index: 1;
            transition: width 0.3s ease-in-out;

            &:after {
              position: absolute;
              content: '';
              left: 1px;
              top: 0;
              border-radius: 4px;
              height: 100%;
              width: calc(100% - 2px);
              background: linear-gradient(264deg, #29e8c4 0%, rgba(31, 195, 164, 0.4) 100%);
            }
          }
        }
      }
    }

    .chart-box {
      flex: 1;
      width: 100%;
    }
  }

  @media screen and (min-width: 1800px) {
    .carbon-content {
      .px2vw(6);
      padding: @vw;
    }
  }

  @media screen and (min-width: 2000px) {
    .carbon-content {
      .target-box {
        .target-header {
          .title {
            .font-size(16);
          }

          .value-unit {
            .value {
              .font-size(16);
            }

            .unit {
              .font-size(14);
            }

            .proportion {
              .font-size(16);
            }
          }
        }

        .target-process {
          .width-prop(8, margin-top);

          .process-bar {
            .height-prop(20);

            .bar-inner {
              .height-prop(20);
            }
          }
        }
      }
    }
  }
</style>
