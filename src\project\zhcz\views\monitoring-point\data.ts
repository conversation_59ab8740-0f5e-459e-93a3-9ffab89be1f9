import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { DICT } from '/@/enums/dict';
import { getDictTypeListApi } from '/@/api/admin/dict';

export const columns: BasicColumn[] = [
  {
    title: '测点编码',
    dataIndex: 'indicatorCode',
    width: 280,
  },
  {
    title: '采集标签',
    dataIndex: 'gatherCode',
  },
  {
    title: '测点名称',
    dataIndex: 'indicatorName',
  },
  {
    title: '数据来源',
    dataIndex: 'resourceType',
    width: 100,
  },
  {
    title: '时间类型',
    dataIndex: 'timeType',
    width: 120,
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    width: 100,
  },
  {
    title: '同类标签',
    dataIndex: 'tag',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 180,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'indicatorCode',
    label: '测点编码',
    labelWidth: 68,
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'indicatorName',
    label: '测点名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'indicatorDesc',
    label: '测点描述',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'sourceType',
    label: '数据来源',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.MONITORING_POINT_DATA_SOURCE },
      valueField: 'intValue',
    },
  },
  {
    field: 'dataType',
    label: '数据类型',
    labelWidth: 68,
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.MONITORING_POINT_DATA_TYPE },
    },
  },
  {
    field: 'dataCalcType',
    label: '计算类型',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.MONITORING_POINT_DATA_DALC_TYPE },
      valueField: 'intValue',
    },
    show: false,
  },
  {
    field: 'tag',
    label: '同类标签',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'dbSource',
    component: 'ApiSelect',
    label: '数据库源',
    colProps: { span: 6 },
    show: false,
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.DB_SOURCE },
    },
  },
];

export const schemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'Id',
    show: false,
  },
  {
    field: 'factoryId',
    component: 'Input',
    label: '工厂Id',
    show: false,
    defaultValue: 2000,
  },
  {
    field: 'indicatorCode',
    component: 'Input',
    label: '测点编码',
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'indicatorName',
    component: 'Input',
    label: '测点名称',
    labelWidth: 100,
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'gatherCode',
    component: 'Input',
    label: '采集标签',
    colProps: { span: 12 },
  },
  {
    field: 'indicatorDesc',
    component: 'Input',
    label: '测点描述',
    labelWidth: 100,
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'sourceType',
    component: 'Select',
    label: '数据来源',
    slot: 'source-type-slot',
    colProps: { span: 12 },
    required: true,
  },
  // {
  //   field: 'dataTimeType',
  //   component: 'ApiSelect',
  //   label: '时间类型',
  //   colProps: { span: 12 },
  //   required: true,
  //   componentProps: {
  //     api: getDictTypeListApi,
  //     params: { type: DICT.MONITORING_POINT_DATA_TIME_TYPE },
  //     valueField: 'intValue',
  //   },
  // },
  {
    field: 'dataTimeType',
    component: 'Select',
    label: '时间类型',
    slot: 'time-type-slot',
    labelWidth: 100,
    colProps: { span: 12 },
    required: true,
    // componentProps: {
    //   api: getDictTypeListApi,
    //   params: { type: DICT.MONITORING_POINT_DATA_TIME_TYPE },
    //   valueField: 'intValue',
    // },
  },
  {
    field: 'dataType',
    component: 'Select',
    label: '数据类型',
    slot: 'data-slot',
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'dataCalcType',
    component: 'ApiSelect',
    label: '计算类型',
    labelWidth: 100,
    colProps: { span: 12 },
    required: true,
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.MONITORING_POINT_DATA_DALC_TYPE },
    },
    defaultValue: '-1',
    show: false,
  },
  {
    field: 'searchTarget',
    component: 'Input',
    label: '公式',
    slot: 'target-slot',
    colProps: { span: 24 },
    ifShow: false,
  },
  {
    field: 'formula',
    component: 'Input',
    label: '公式',
    slot: 'formula-slot',
    colProps: { span: 24 },
    required: true,
    ifShow: false,
  },
  {
    field: 'isZero',
    component: 'Switch',
    label: '可为0',
    colProps: { span: 6 },
    componentProps: {
      checkedValue: '1',
      unCheckedValue: '0',
    },
    defaultValue: '0',
    show: false,
  },
  {
    field: 'dbSource',
    component: 'ApiSelect',
    label: '数据库源',
    colProps: { span: 12 },
    show: false,
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.DB_SOURCE },
    },
  },
  {
    field: 'isNull',
    component: 'Switch',
    show: false,
    label: '可为空',
    colProps: { span: 6 },
    componentProps: {
      checkedValue: '1',
      unCheckedValue: '0',
    },
    defaultValue: '1',
  },
  {
    field: 'sortId',
    component: 'Input',
    label: '排序',
    show: false,
    defaultValue: 0,
  },
  {
    field: 'unit',
    component: 'Input',
    componentProps: {
      placeholder: 'mg/L',
    },
    label: '默认单位',
    labelWidth: 100,
    colProps: { span: 12 },
  },
  {
    field: 'digit',
    component: 'InputNumber',
    componentProps: {
      placeholder: '2',
    },
    label: '默认小数位',
    colProps: { span: 12 },
  },
  {
    field: 'maxVal',
    component: 'InputNumber',
    componentProps: {
      placeholder: '100',
    },
    label: '默认上限',
    labelWidth: 100,
    colProps: { span: 12 },
  },
  {
    field: 'minVal',
    component: 'InputNumber',
    componentProps: {
      placeholder: '0',
    },
    label: '默认下限',
    colProps: { span: 12 },
  },
  {
    field: 'mergeType',
    component: 'ApiSelect',
    colProps: { span: 12 },
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.MONITORING_POINT_DATA_DALC_TYPE },
    },
    label: '合计类型',
    labelWidth: 100,
  },
  // {
  //   field: 'simpleName',
  //   component: 'Input',
  //   label: '测点简称',
  //   labelWidth: 100,
  //   colProps: { span: 12 },
  // },
];

export const mockData = {
  total: '133',
  list: [
    {
      id: '806',
      parentId: null,
      weight: 0,
      name: 'Node 806',
      indicatorName: '2#导叶',
      dbSource: 'mysql',
      createTime: '2024-01-12 12:23:25',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.LCP2_DY',
      timeType: '实时数据',
      updateTime: '2024-01-12 12:23:25',
      tag: 'Smart.M580.GFJ.LCP2_DY',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '807',
          parentId: '806',
          weight: 1,
          name: 'Node 807',
          indicatorName: '2#导叶_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 12:23:27',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.LCP2_DY_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 12:23:27',
          tag: 'Smart.M580.GFJ.LCP2_DY',
          parentIndicatorCode: 'Smart.M580.GFJ.LCP2_DY',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '808',
      parentId: null,
      weight: 0,
      name: 'Node 808',
      indicatorName: '3#导叶',
      dbSource: 'mysql',
      createTime: '2024-01-12 12:24:02',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.LCP3_DY',
      timeType: '实时数据',
      updateTime: '2024-01-12 12:24:02',
      tag: 'Smart.M580.GFJ.LCP3_DY',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '809',
          parentId: '808',
          weight: 1,
          name: 'Node 809',
          indicatorName: '3#导叶_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 12:24:02',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.LCP3_DY_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 12:24:02',
          tag: 'Smart.M580.GFJ.LCP3_DY',
          parentIndicatorCode: 'Smart.M580.GFJ.LCP3_DY',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '810',
      parentId: null,
      weight: 0,
      name: 'Node 810',
      indicatorName: '4#导叶',
      dbSource: 'mysql',
      createTime: '2024-01-12 12:24:24',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.LCP4_DY',
      timeType: '实时数据',
      updateTime: '2024-01-12 12:24:24',
      tag: 'Smart.M580.GFJ.LCP4_DY',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '811',
          parentId: '810',
          weight: 1,
          name: 'Node 811',
          indicatorName: '4#导叶_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 12:24:24',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.LCP4_DY_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 12:24:24',
          tag: 'Smart.M580.GFJ.LCP4_DY',
          parentIndicatorCode: 'Smart.M580.GFJ.LCP4_DY',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '812',
      parentId: null,
      weight: 0,
      name: 'Node 812',
      indicatorName: '5#导叶',
      dbSource: 'mysql',
      createTime: '2024-01-12 12:25:19',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.LCP5_DY',
      timeType: '实时数据',
      updateTime: '2024-01-12 12:25:19',
      tag: 'Smart.M580.GFJ.LCP5_DY',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '813',
          parentId: '812',
          weight: 1,
          name: 'Node 813',
          indicatorName: '5#导叶_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 12:25:19',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.LCP5_DY_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 12:25:19',
          tag: 'Smart.M580.GFJ.LCP5_DY',
          parentIndicatorCode: 'Smart.M580.GFJ.LCP5_DY',
          resourceType: '采集',
        },
        {
          id: '1218',
          parentId: '812',
          weight: 1,
          name: 'Node 1218',
          indicatorName: '5#导叶_分钟_累加值',
          dbSource: 'mysql',
          createTime: '2024-03-08 15:17:07',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.LCP5_DY_MINUTE_SUM',
          timeType: '分钟数据',
          updateTime: '2024-03-08 15:17:07',
          tag: 'Smart.M580.GFJ.LCP5_DY',
          parentIndicatorCode: 'Smart.M580.GFJ.LCP5_DY',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '814',
      parentId: null,
      weight: 0,
      name: 'Node 814',
      indicatorName: '6#导叶',
      dbSource: 'mysql',
      createTime: '2024-01-12 12:26:21',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.LCP6_DY',
      timeType: '实时数据',
      updateTime: '2024-01-12 12:26:21',
      tag: 'Smart.M580.GFJ.LCP6_DY',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '815',
          parentId: '814',
          weight: 1,
          name: 'Node 815',
          indicatorName: '6#导叶_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 12:26:21',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.LCP6_DY_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 12:26:21',
          tag: 'Smart.M580.GFJ.LCP6_DY',
          parentIndicatorCode: 'Smart.M580.GFJ.LCP6_DY',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '816',
      parentId: null,
      weight: 0,
      name: 'Node 816',
      indicatorName: '7#导叶',
      dbSource: 'mysql',
      createTime: '2024-01-12 12:27:11',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.LCP7_DY',
      timeType: '实时数据',
      updateTime: '2024-01-12 12:27:11',
      tag: 'Smart.M580.GFJ.LCP7_DY',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '817',
          parentId: '816',
          weight: 1,
          name: 'Node 817',
          indicatorName: '7#导叶_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 12:27:12',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.LCP7_DY_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 12:27:12',
          tag: 'Smart.M580.GFJ.LCP7_DY',
          parentIndicatorCode: 'Smart.M580.GFJ.LCP7_DY',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '818',
      parentId: null,
      weight: 0,
      name: 'Node 818',
      indicatorName: '8#导叶',
      dbSource: 'mysql',
      createTime: '2024-01-12 12:28:05',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.LCP8_DY',
      timeType: '实时数据',
      updateTime: '2024-01-12 12:28:05',
      tag: 'Smart.M580.GFJ.LCP8_DY',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '819',
          parentId: '818',
          weight: 1,
          name: 'Node 819',
          indicatorName: '8#导叶_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 12:28:06',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.LCP8_DY_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 12:28:06',
          tag: 'Smart.M580.GFJ.LCP8_DY',
          parentIndicatorCode: 'Smart.M580.GFJ.LCP8_DY',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '820',
      parentId: null,
      weight: 0,
      name: 'Node 820',
      indicatorName: '鼓风机压力反馈',
      dbSource: 'mysql',
      createTime: '2024-01-12 12:28:47',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.YL_PV',
      timeType: '实时数据',
      updateTime: '2024-01-12 12:28:47',
      tag: 'Smart.M580.GFJ.YL_PV',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '821',
          parentId: '820',
          weight: 1,
          name: 'Node 821',
          indicatorName: '鼓风机压力反馈_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 12:28:47',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.YL_PV_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 12:28:47',
          tag: 'Smart.M580.GFJ.YL_PV',
          parentIndicatorCode: 'Smart.M580.GFJ.YL_PV',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '822',
      parentId: null,
      weight: 0,
      name: 'Node 822',
      indicatorName: '鼓风机压力设定',
      dbSource: 'mysql',
      createTime: '2024-01-12 14:17:04',
      dataType: '未设置信号',
      indicatorCode: 'Smart.M580.GFJ.YL_SV',
      timeType: '实时数据',
      updateTime: '2024-01-12 14:17:04',
      tag: 'Smart.M580.GFJ.YL_SV',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '823',
          parentId: '822',
          weight: 1,
          name: 'Node 823',
          indicatorName: '鼓风机压力设定_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 14:17:04',
          dataType: '未设置信号',
          indicatorCode: 'Smart.M580.GFJ.YL_SV_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 14:17:04',
          tag: 'Smart.M580.GFJ.YL_SV',
          parentIndicatorCode: 'Smart.M580.GFJ.YL_SV',
          resourceType: '采集',
        },
      ],
    },
    {
      id: '826',
      parentId: null,
      weight: 0,
      name: 'Node 826',
      indicatorName: '1、2廊道DO反馈',
      dbSource: 'mysql',
      createTime: '2024-01-12 14:18:34',
      dataType: '未设置信号',
      indicatorCode: 'MUCT1.S7400.TO_580.QD_ADO',
      timeType: '实时数据',
      updateTime: '2024-01-12 14:18:34',
      tag: 'MUCT1.S7400.TO_580.QD_ADO',
      parentIndicatorCode: null,
      resourceType: '采集',
      children: [
        {
          id: '827',
          parentId: '826',
          weight: 1,
          name: 'Node 827',
          indicatorName: 'MUCT1_A组1、2廊道DO反馈_分钟_平均值',
          dbSource: 'mysql',
          createTime: '2024-01-12 14:18:34',
          dataType: '未设置信号',
          indicatorCode: 'MUCT1.S7400.TO_580.QD_ADO_MINUTE_AVG',
          timeType: '分钟数据',
          updateTime: '2024-01-12 14:18:34',
          tag: 'MUCT1.S7400.TO_580.QD_ADO',
          parentIndicatorCode: 'MUCT1.S7400.TO_580.QD_ADO',
          resourceType: '采集',
        },
      ],
    },
  ],
};
