import dayjs from 'dayjs';
import type { AerationData, BarChartData, DataList } from './type';

export const colorList = [
  ['rgba(26, 0, 170, 1)', 'rgba(26, 0, 170, 0.12)', 'rgba(26, 0, 170, 0)'],
  ['rgba(172, 195, 0, 1)', 'rgba(172, 195, 0, 0.12)', 'rgba(172, 195, 0, 0)'],
  ['rgba(0, 146, 136, 1)', 'rgba(0, 146, 136, 0.12)', 'rgba(0, 146, 136, 0)'],
  ['rgba(4, 116, 185, 1)', 'rgba(4, 116, 185, 0.12)', 'rgba(4, 116, 185, 0)'],
  ['rgba(0, 143, 199, 1)', 'rgba(0, 143, 199, 0.12)', 'rgba(0, 143, 199, 0)'],
  ['rgba(195, 117, 0, 1)', 'rgba(195, 117, 0, 0.12)', 'rgba(195, 117, 0, 0)'],
  ['rgba(195, 142, 0, 1)', 'rgba(195, 142, 0, 0.12)', 'rgba(195, 142, 0, 0)'],
  ['rgba(92, 89, 255, 1)', 'rgba(92, 89, 255, 0.12)', 'rgba(92, 89, 255, 0)'],
  ['rgba(172, 195, 0, 1)', 'rgba(172, 195, 0, 0.12)', 'rgba(172, 195, 0, 0)'],
  ['rgba(26, 0, 170, 1)', 'rgba(26, 0, 170, 0.12)', 'rgba(26, 0, 170, 0)'],
  ['rgba(62, 0, 170, 1)', 'rgba(62, 0, 170, 0.12)', 'rgba(62, 0, 170, 0)'],
];

// 综合管理驾驶舱
export const indexCodeList = [
  {
    resourceInterfaceId: '12',
    groupCode: 'ZHGLJSC_BQNX',
    name: '曝气能效',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '3',
    groupCode: 'ZHGLJSC_SCLNX',
    name: '水处理能效',
  },
  {
    resourceInterfaceId: '1935511800297021441',
    groupCode: 'TZHJD_JD',
    name: '碳中和进度-进度',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '3',
    groupCode: 'TZHJD_NDTPFL',
    name: '碳中和进度-排放量',
  },
  {
    resourceInterfaceId: '1937788922696945666',
    groupCode: 'TQDFX_L',
    name: '碳强度分析',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '1937788922696945666',
    groupCode: 'ZHGLJSC_ZHTJ',
    name: '综合统计',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '3',
    groupCode: 'RQDFX_TQD',
    name: '碳强度分析-碳强度',
  },
  {
    resourceInterfaceId: '1937788922696945666',
    groupCode: 'ZHJSC_JJXY',
    name: '经济效益',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '3',
    groupCode: 'DFQS_JQBQDF',
    name: '精确曝气电费',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '3',
    groupCode: 'DFQS_CTDF',
    name: '传统电费',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '3',
    groupCode: 'ZHJSC_TSYQS',
    name: '碳收益趋势',
  },
];

export const dateTypeOptions = [
  // {
  //   value: 'day',
  //   label: '日',
  // },
  {
    value: 'month',
    label: '月',
  },
];

export const lineChartIcon1 =
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAu9JREFUSEu1VktoE1EUfdMkkzaZ/LRpLG0ksSL9Ie3CKm5aKnWji5SuFMTMQnAh0oCCoJAGFASFFHEhuEhE0FVJFrqx2M9GtC5atR8Ra0LT2KapZprMJGaSFLkPX5jW/Ero3Ux4b3LO/Z47FCph9oDP8rBpoG8ty182ylQWRl6rh9f57B9ORNtzsUxyeiIT8ToM3VwxGKrQBQDfbDjtbFc12Es5QO4iGX70pfDdVYjoP4JAOmYzytUedY0Ce1upCdsZLpoVWKvS4Jf+ZwfBh0RopIdpdkpf+Mivordby2gi/gPFc2l81URrUWudEdkMbegE07zDhxl+1XVSYx4hh3mCpVS0r7W2fpJchMU4uh0aR0BQynqYZnTXPIBJiQVFbpBEggncsVn9FW1HgKTlayqK7MtjKPHP43JpAvBHlvM4KjBI17WV191e62AQE6yLCbdJwQzDb/CcXR7Dz72YRqZEY8cu5iNZTG54O9QmlgLvh/VdMQIG4DNl0lKMGOrhbRnKX7NBv5X6xP8cPq5udMMp5BtSU40BASn8r4zAUitpzmemdTYAhaL6fy9Wg48u1XehW029GGM+FZmi1tPxgInWWOBg6NsLBAWuxqDgb9pYDBERE0GKz4kx0j2n5p9U3DnFnIBiv++8mu+m/SeQpujskmfP7bk7EpgFaNd8ir4k1yc760x9cHA/PI2eb85VUwJkO9CO7pkHMEZI3PJTmyJvP6hQe/ajTT8Law4KpNljsQWI2zAH5fSnWIigSx7JoI1ycwYsFQtCxEO0HyQC2rVSHSJk0J4ATkQPdsQhWuPABBDF48PnZqVidz34quKCQ2vCBEvF7ml8wQoLKC/XsGgstN5HPIJI7oTGy+oSyAIUtaRcE9BiC8cfW8ITThRWK1Oifu0RdEbXUvnCISSweMy0zrcvK5OQgIRfUB91kh1RbjBA+x9svHPBgtn9bsGvCilRv8JkNyhUvTSq6ZJ+tkRzyWCjnHl2Izw+VQiYYPwF1ktx6ijfhHMAAAAASUVORK5CYII=';
export const lineChartIcon2 =
  'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAvVJREFUSEu1Vk9QEmEUf4vAggKBJoZGYTbNJJmah8qa4lQdbcasTulktw7SsQty6RgeumWjnSprRo/VRWvK6mBqDjXTpFCkJP5hY5Fl2QWbt823rQaIw/hOO9/39vd733vv+72PgjwWnrziqHTec4nJn9dUGqujRGM0o3taYBlqPTUl8CuveOb1oKXWzeSCobJtIHB53W2P1tjQmS8Asify4b54+Ik3G9F/BPzatzY1XTWgKjFI0RZqmXScEfnFLrrs4Ijynw0Eaytve0vLWz1KBy76DtaWnkM88gIy4m9pS6O3g9bgBFN1B+gtJzfEkFgd95ZVnOolizIBx864dIYjo2RD4EIQ+ewGJMhneksrWOvvSqTEUonZi+QkEkE04DOb7N0Bkhae9cP8RDtkxFhBGVLr7GBrfAC00Sn5Y7oiMzeabc2PgxKBkFzwqWlbj/TNhWB+4hKIyVBB4MRJpTaB/fhL+SQpdmaQNh3tojB6s6MnShwRnIuObwucOGM9alqeyf/+mrpaSyWiEz168zEfrmK+MTXFGBKQwqeFlS5KSASH1fr9bQi66HcDGx4qBh/M+7ph9yGvhMGz02NUilsIaHQ2By6EPpwDLHAxhgV3nH4vQQjJcJBKi2yUdM/c2OGCOydXEFjsA64vcjftPIEyRcE3J7bdnptPgncB21VOUTI2NUobG124sPzVA8yP/mJKAEZbB1Q5paYEkfs+Qomp5c4STcXATrQpx3x0UyjNe5oeBf5dtPYt9SfXEVGXalqeyttMsM8iSQUf+zRAtB+lAtu1UB0iaNieCE5ED2eERlftlgjwFNaG+5NKsQtPXy+44NiaeIOVYhcL9dfiAJLlGgeNtrRumET0V65vbalLKAvWel9+uSaguQZObGEIUnG/pLRoKvUuMFjPQ1nlhcIHDiHBwaPVO4Z3ZGQSEpRwg+2yh8yIrS4Gav/q7B0vDpjNvllfFUoi2nymU0NXnF2ntE3KZ0tGiATVur0Pl/w3x7IBE4w/TkVzQgH3K6sAAAAASUVORK5CYII=';

export const centerParams = [
  {
    resourceInterfaceId: '1937788922696945666', // (多指标列表数据合并)获取采集数据一段时间范围内的数据集合
    groupCode: 'ZHGLJSC_ZHTJ',
    name: '综合统计',
    startDateTime: dayjs().format('1970-01-01 00:00:00'),
    endDateTime: dayjs().format('YYYY-MM-DD 23:59:59'),
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'ZHJSC_ZXSJ_JSSZ',
    name: '进水水质',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'ZHJSC_ZXSJ_CSSZ',
    name: '出水水质',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'ZHJSC_ZXSJ_GFJF',
    name: '鼓风机房',
    jsConvert: true,
  },
];
// 碳强度分析-统计mock
export const mockCarbonStrengthTotalData = [
  {
    value: null,
    name: '碳排放量',
    unit: '',
    digit: 0,
    indexCode: '',
    maxVal: null,
    minVal: null,
  },
  {
    value: null,
    name: '水处理量',
    unit: '',
    digit: 0,
    indexCode: '',
    maxVal: null,
    minVal: null,
  },
];

// 峰平谷用电-统计mock
export const mockElectricityStatisticsData: AerationData[] = [
  {
    name: '峰',
    value: '',
    unit: 'kWh',
    digit: 0,
    maxVal: null,
    minVal: null,
    indexCode: '',
  },
  {
    name: '峰尖',
    value: '',
    unit: 'kWh',
    digit: 0,
    maxVal: null,
    minVal: null,
    indexCode: '',
  },
  {
    name: '平',
    value: '',
    unit: 'kWh',
    digit: 0,
    maxVal: null,
    minVal: null,
    indexCode: '',
  },
  {
    name: '谷',
    value: '',
    unit: 'kWh',
    digit: 0,
    maxVal: null,
    minVal: null,
    indexCode: '',
  },
  {
    name: '谷底',
    value: '',
    unit: 'kWh',
    digit: 0,
    maxVal: null,
    minVal: null,
    indexCode: '',
  },
  {
    name: '当前电价',
    value: '',
    unit: '元',
    digit: 2,
    maxVal: null,
    minVal: null,
    indexCode: '',
  },
];

// 电费趋势-图表mock
export const mockElectricityData: BarChartData[] = Array.from(
  { length: Number(dayjs().format('MM')) },
  (_, index) => ({
    name: '电费',
    value: null,
    digit: 2,
    unit: '万元',
    indexCode: 'MN_JQBQDF',
    collectDateTime: dayjs().format(`YYYY-${index + 1}-01 00:00:00`),
  }),
);

// 中心流量mock
export const mockWaterFlowData: DataList[] = [
  {
    unit: 'm³',
    indexCode: '',
    name: '总进水量',
    value: '',
    digit: 0,
  },
  {
    unit: 'kW.h',
    indexCode: '',
    name: '总电耗',
    value: '',
    digit: 0,
  },
  {
    unit: 'kgCO₂e​',
    indexCode: '',
    name: '总碳排放量',
    value: '',
    digit: 0,
  },
  // {
  //   unit: 'm³/h',
  //   indexCode: '',
  //   name: '总风量',
  //   value: '',
  //   digit: 0,
  // },
];

// 水处理能效mock
export const mockWaterData = Array.from({ length: 1 }, () => {
  const len = Number(dayjs().format('MM'));
  return {
    name: '吨水电耗',
    data: Array.from({ length: len }, (_, i) => ({
      collectDateTime: dayjs().format(`YYYY-${i + 1}-01 00:00:00`),
      value: null,
    })),
    digit: 2,
    unit: 'kWh/m³',
    maxVal: 0,
    minVal: 0,
    indexCode: 'MN_JQBQ_DSDH_L_AVG',
  };
});

// 碳中和mock
export const mockCarbonEmissionsData = [
  {
    name: '碳排量',
    data: Array.from({ length: Number(dayjs().format('MM')) }, (_, index) => ({
      collectDateTime: dayjs().format(`YYYY-${index + 1}-01 00:00:00`),
      value: null,
    })),
    digit: 0,
    unit: 'kgCO₂e​',
    maxVal: 0,
    minVal: 0,
    indexCode: 'MN_TPF_L_SUM',
  },
];

// 碳强度mock
export const mockCarbonStrengthData = (startDate: string) => {
  const len = Number(dayjs(startDate).endOf('month').format('DD'));
  const month = dayjs(startDate).format('MM');
  const year = dayjs(startDate).format('YYYY');
  return [
    {
      name: '碳强度',
      data: Array.from({ length: len }, (_, index) => ({
        collectDateTime: dayjs().format(`${year}-${month}-${index + 1} 00:00:00`),
        value: null,
      })),
      digit: 1,
      unit: 'tCO₂e​',
      maxVal: 0,
      minVal: 0,
      indexCode: 'MN_TPF_L_SUM',
    },
  ];
};

// 碳收益
export const mockCarbonProfitData = [
  {
    unit: '万元',
    indexCode: 'MN_TSY',
    name: '碳收益',
    tag: 'MN_TSY',
    maxVal: 0,
    minVal: 0,
    digit: 2,
    data: Array.from({ length: Number(dayjs().format('MM')) }, (_, index) => ({
      collectDateTime: dayjs().format(`YYYY-${index + 1}-01 00:00:00`),
      value: null,
    })),
  },
];

// 经济效益
export const mockBenefitData = Array.from({ length: 2 }, (_, index) => ({
  name: index % 2 ? '碳收益' : '电费节省',
  value: null,
  digit: 2,
  unit: '元',
  indexCode: '',
  maxVal: 0,
  minVal: 0,
}));

// 1536 1920x125%
const LAPTOP_WIDTH_SCALE_125 = 1536;
export const getScaleValByClientWidth = (val, scale = 1.25) => {
  const clientWidth =
    window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;

  if (clientWidth <= LAPTOP_WIDTH_SCALE_125) {
    return val / scale;
  }

  return (val * clientWidth) / 1920;
};

export const monthMap = {
  1: '一',
  2: '二',
  3: '三',
  4: '四',
  5: '五',
  6: '六',
  7: '七',
  8: '八',
  9: '九',
  10: '十',
  11: '十一',
  12: '十二',
};
