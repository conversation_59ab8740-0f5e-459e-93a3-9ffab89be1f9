<template>
  <div class="ai-box h-full">
    <div class="ai-content">
      <div class="ai-question-box flex flex-row-reverse" v-if="aiData.aiQuestion && step >= 1">
        <div class="ai-question p-3">{{ aiData.aiQuestion }}</div>
      </div>
      <div class="ai-answer flex mt-6" v-if="step >= 1">
        <div class="logo-box flex items-center justify-center">
          <img :src="deepseekLogo" alt="" />
        </div>
        <div class="ai-answer-right flex-1 pl-3">
          <div class="depth-des flex items-center">
            <img class="mr-1" :src="deeplyLogo" alt="" />
            <span
              >已深度思考<span v-if="finallyed">（用时{{ time }}s）</span></span
            >
          </div>
          <div class="deep-analysis pl-2.5" v-html="displayedAnalysisText"></div>
          <div v-if="step >= 2">
            <div class="explain" v-html="displayedExplainText"></div>
            <div class="result-title" v-html="displayedTitleText"></div>
            <div class="result-des" v-html="displayedResultText"></div>
            <!-- <div class="result-des" v-for="(item, index) in aiData.resultDes" :key="index">
              <div class="result-des-title">{{ item.firstTitle }}</div>
              <div
                class="result-des-content"
                v-for="(twoItem, twoIndex) in item.firstContent"
                :key="twoIndex"
              >
                <div class="result-des-twotitle">{{ twoItem.twoTitle }}</div>
                <div class="result-des-twocontent-box">
                  <div
                    class="result-des-twocontent"
                    v-for="(threeItem, threeIndex) in twoItem.twoContent"
                    :key="threeIndex"
                    ><span class="result-des-twocontent-line"></span>{{ threeItem.content }}</div
                  >
                </div>
              </div>
            </div> -->
            <div class="summary-title" v-html="displayedSummaryTitleText"></div>
            <div class="summary-content" v-html="displayedSummaryText"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, onMounted, computed } from 'vue';
  import deepseekLogo from '/@/assets/images/deepseek-logo_white.png';
  import deeplyLogo from '/@/assets/images/deeply-logo_white.png';
  import { setTimer, radomNumber } from '../helper/index';

  export default defineComponent({
    props: {
      speed: {
        type: Number,
        default: 1,
      },
      aiData: {
        type: Object,
        default: () => {
          return {
            aiQuestion: '',
            deepAnalysis: '',
            explain: '',
            resultDes: '',
            summaryTitle: '',
            summary: '',
          };
        },
      },
    },
    setup(props) {
      const step = ref(0); // 0: init, 1: deep analysis, 2: result
      const displayedAnalysisText = ref('');
      const displayedExplainText = ref('');
      const displayedTitleText = ref('');
      const displayedResultText = ref('');
      const displayedSummaryTitleText = ref('');
      const displayedSummaryText = ref('');
      const time = ref(0);
      const finallyed = ref(false);

      const displayedAText = computed(() => {
        return props.aiData.deepAnalysis.split('');
      });
      const initFun = async () => {
        await setTimer(600 * props.speed, null);
        step.value++;
        await setTimer(500 * props.speed, null);
        await displayText(props.aiData.deepAnalysis);
        step.value++;
        await displayExplainText(props.aiData.explain);
        step.value++;
        await displayTitleText(props.aiData.resultTitle);
        await displayResulText(props.aiData.resultDes);
        await displaySummaryTitleText(props.aiData.summaryTitle);
        await displaySummaryText(props.aiData.summary);
        time.value = radomNumber(1, 10);
        finallyed.value = true;
      };

      // 深度思考
      const displayText = (text: string) => {
        return new Promise((resolve) => {
          let index = 0;
          const interval = setInterval(() => {
            if (index < text.length) {
              if (text[index] === '\n') {
                displayedAnalysisText.value += '<p class="my-3" />';
              } else {
                displayedAnalysisText.value += text[index];
              }
              index++;
            } else {
              clearInterval(interval);
              resolve(true);
            }
          }, 30 * props.speed); // Adjust the speed of character display
        });
      };

      // 说明内容
      const displayExplainText = (text: string) => {
        return new Promise((resolve) => {
          let index = 0;
          const interval = setInterval(() => {
            if (index < text.length) {
              if (text[index] === '\n') {
                displayedExplainText.value += '<p class="my-3" />';
              } else {
                displayedExplainText.value += text[index];
              }
              index++;
            } else {
              clearInterval(interval);
              resolve(true);
            }
          }, 30 * props.speed); // Adjust the speed of character display
        });
      };

      // 分析标题
      const displayTitleText = (text: string) => {
        return new Promise((resolve) => {
          let index = 0;
          const interval = setInterval(() => {
            if (index < text.length) {
              if (text[index] === '\n') {
                displayedTitleText.value += '<p class="my-3" />';
              } else {
                displayedTitleText.value += text[index];
              }
              index++;
            } else {
              clearInterval(interval);
              resolve(true);
            }
          }, 30 * props.speed); // Adjust the speed of character display
        });
      };

      // 分析结果
      const displayResulText = (text: string) => {
        return new Promise((resolve) => {
          let index = 0;
          const interval = setInterval(() => {
            if (index < text.length) {
              if (text[index] === '\n') {
                displayedResultText.value += '<p class="my-3" />';
              } else {
                displayedResultText.value += text[index];
              }
              index++;
            } else {
              clearInterval(interval);
              resolve(true);
            }
          }, 30 * props.speed); // Adjust the speed of character display
        });
      };

      // 总结标题
      const displaySummaryTitleText = (text: string) => {
        return new Promise((resolve) => {
          let index = 0;
          const interval = setInterval(() => {
            if (index < text.length) {
              if (text[index] === '\n') {
                displayedSummaryTitleText.value += '<p class="my-3" />';
              } else {
                displayedSummaryTitleText.value += text[index];
              }
              index++;
            } else {
              clearInterval(interval);
              resolve(true);
            }
          }, 30 * props.speed); // Adjust the speed of character display
        });
      };
      // 总结内容
      const displaySummaryText = (text: string) => {
        return new Promise((resolve) => {
          let index = 0;
          const interval = setInterval(() => {
            if (index < text.length) {
              if (text[index] === '\n') {
                displayedSummaryText.value += '<p class="my-3" />';
              } else {
                displayedSummaryText.value += text[index];
              }
              index++;
            } else {
              clearInterval(interval);
              resolve(true);
            }
          }, 30 * props.speed); // Adjust the speed of character display
        });
      };

      onMounted(() => {
        initFun();
      });
      return {
        deepseekLogo,
        deeplyLogo,
        step,
        displayedAnalysisText,
        displayedExplainText,
        displayedTitleText,
        displayedResultText,
        displayedSummaryTitleText,
        displayedSummaryText,
        time,
        finallyed,
        displayedAText,
      };
    },
  });
</script>
<style lang="less" scoped>
  .ai-content {
    color: #333;
    padding-bottom: 50px;
    text-align: justify;

    .logo-box {
      width: 40px;
      height: 40px;
      border: 1px solid rgba(11, 98, 203, 0.56);
      border-radius: 50%;

      img {
        width: 24px;
        height: 24px;
      }
    }

    .ai-question-box {
      .ai-question {
        width: 508px;
        font-size: 15px;
        border-radius: 4px;
        text-align: justify;
        background-color: rgba(11, 98, 203, 0.08);
      }
    }

    .ai-answer-right {
      .depth-des {
        width: fit-content;
        height: 32px;
        margin: 4px 0;
        padding: 0 12px;
        border-radius: 4px;
        background-color: #f1f4f7;

        img {
          width: 13px;
          height: 13px;
        }
      }

      .deep-analysis {
        color: #666;
        text-align: justify;
        line-height: 21px;
        margin: 12px 0;
        border-left: 2px solid #d8d8d8;
      }

      .explain {
        font-size: 15px;
        line-height: 21px;
        padding: 4px 0 16px;
      }

      .result-title {
        font-size: 17px;
        font-weight: 600;
        line-height: 17px;
        margin: 1px 0 12px;
      }

      .result-des {
        // padding-left: 12px;
        font-size: 15px;
        color: #333333;
        line-height: 21px;
        text-align: justify;

        .result-des-title {
          font-size: 17px;
          font-weight: 600;
          line-height: 17px;
          margin-bottom: 16px;
        }

        .result-des-content {
          padding-left: 12px;

          .result-des-twotitle {
            font-size: 15px;
            font-weight: 600;
            line-height: 14px;
          }

          .result-des-twocontent-box {
            margin: 12px 0;
          }

          .result-des-twocontent {
            padding-left: 12px;
            font-size: 15px;
            color: #333333;
            line-height: 21px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;

            .result-des-twocontent-line {
              display: inline-block;
              width: 3px;
              height: 3px;
              background-color: #333;
              border-radius: 100%;
              margin-right: 6px;
            }
          }
        }
      }

      .summary-title {
        font-weight: 600;
        font-size: 17px;
        line-height: 17px;
        margin: 20px 0 12px;
      }

      .summary-content {
        font-size: 15px;
        line-height: 21px;
      }
    }
  }
</style>
