<template>
  <div class="event">
    <a-collapse
      :bordered="false"
      ghost
      v-model:activeKey="activeKey"
      class="my-collapse"
      @change="onCollapseChange"
    >
      <a-collapse-panel key="绑定设备流水线" header="绑定设备流水线">
        <a-form
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          :colon="false"
          labelAlign="left"
          autocomplete="off"
        >
          <!-- 显示详情 -->
          <a-form-item label="设备流水线">
            <a-select
              v-model:value="flowForm.flowId"
              style="width: 100%"
              @change="handleFlowChange"
              :allow-clear="!!flowForm.flowId"
            >
              <a-select-option v-for="item in flowOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="行为类型">
            <a-select
              v-model:value="flowForm.operation"
              style="width: 100%"
              @change="handleOperationChange"
              :allow-clear="!!flowForm.operation"
            >
              <a-select-option
                v-for="item in operationOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <div class="event_button">
            <a-button @click="confirmFlow" type="primary" style="width: 100%"> 确定事件 </a-button>
            <a-button @click="removeFlow" style="width: 100%"> 清除事件 </a-button>
          </div>
        </a-form>
      </a-collapse-panel>
      <a-collapse-panel key="绑定设备" header="绑定设备">
        <a-form
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          :colon="false"
          labelAlign="left"
          autocomplete="off"
        >
          <!-- 绑定设备 -->
          <a-form-item label="设备">
            <a-select
              v-model:value="deviceForm.eqId"
              show-search
              :filter-option="filterOption"
              @change="handleDeviceChange"
              :allow-clear="!!deviceForm.eqId"
              style="width: 100%"
            >
              <a-select-option v-for="item in eqOptions" :key="item.label" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <div class="event_button">
            <a-button @click="confirmDevice" type="primary" style="width: 30%"> 确认事件 </a-button>
            <a-button @click="removeDevice" style="width: 100%; display: none"> 清除事件 </a-button>
          </div>
        </a-form>
      </a-collapse-panel>
      <a-collapse-panel key="绑定数据集" header="绑定数据集">
        <a-form
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          :colon="false"
          labelAlign="left"
          autocomplete="off"
        >
          <a-form-item label="接口">
            <a-select
              v-model:value="datasetForm.resourceInterfaceId"
              @change="handleInterfaceChange"
              :allow-clear="!!datasetForm.resourceInterfaceId"
            >
              <a-select-option
                v-for="(item, interfaceIndex) in interfaceOptions"
                :key="interfaceIndex"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="数据集">
            <TreeSelect
              v-model:value="datasetForm.groupCode"
              style="width: 100%"
              :tree-data="treeData"
              allow-clear
              :show-checked-strategy="TreeSelect.SHOW_PARENT"
              placeholder="请选择分组"
              show-search
              tree-node-filter-prop="label"
              tree-default-expand-all
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :fieldNames="{
                label: 'label',
                value: 'groupCode',
                children: 'children',
              }"
              @change="onTreeSelectChange"
            />
          </a-form-item>
          <a-form-item label="行为类型">
            <a-select
              v-model:value="datasetForm.actionType"
              placeholder="选择行为类型"
              :allow-clear="!!datasetForm.actionType"
              @change="handleActionTypeChange"
            >
              <a-select-option v-for="i in activeTypeOptions" :key="i.label" :value="i.value">
                {{ i.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <div class="event_button">
            <a-button type="primary" style="width: 100%" @click="handleConfirmEventByDataset">
              确认事件
            </a-button>
            <a-button style="width: 100%" @click="handleCancelEventByDataset"> 清除事件 </a-button>
          </div>
        </a-form>
      </a-collapse-panel>
      <a-collapse-panel key="绑定管线" header="绑定管线">
        <a-form
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          :colon="false"
          labelAlign="left"
          autocomplete="off"
        >
          <a-form-item label="关联管线">
            <a-select
              v-model:value="pipelineForm.ids"
              placeholder="管线"
              mode="tags"
              @change="handlePipelineChange"
              :allow-clear="!!pipelineForm.ids"
            >
              <a-select-option v-for="i in pipelineOptions" :key="i.label" :value="i.value">
                {{ i.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="动态图">
            <HUpload
              :show-file-name="false"
              :max-size="5"
              :api="processFileUploadRemindingApi"
              :dragger="false"
              show-upload-icon
              :isDownload="false"
              v-model:value="fileList"
              @change="handleUploadChange"
              accept="jpeg,jpg,png,.gif"
            >
              <template #upload-icon>
                <div>
                  <a-button type="primary" :ghost="Boolean(fileList?.length)"
                    >{{ fileList?.length ? '重新上传' : '+点击上传' }}
                  </a-button>
                </div>
              </template>
              <template #tips>
                <div class="tips-box" title="支持GIF、JPG、PNG 、GIF，不超过5M">
                  <span>支持GIF、JPG、PNG 、GIF，不超过5M</span>
                </div>
              </template>
            </HUpload>
          </a-form-item>
          <div class="event_button">
            <a-button type="primary" style="width: 100%" @click="handleConfirmEventByPipeline">
              确认事件
            </a-button>
            <a-button style="width: 100%" @click="handleCancelEventByPipeline"> 清除事件 </a-button>
          </div>
        </a-form>
      </a-collapse-panel>
      <a-collapse-panel v-if="activePen?._isModal" key="绑定弹窗" header="绑定弹窗">
        <a-form
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          :colon="false"
          labelAlign="left"
          autocomplete="off"
        >
          <a-form-item label="窗口标题">
            <a-input v-model:value="modalForm.title" placeholder="请输入" />
          </a-form-item>
          <a-form-item label="路由">
            <a-select
              v-model:value="modalForm.route"
              style="width: 100%"
              :allow-clear="!!modalForm.route"
            >
              <a-select-option v-for="item in routeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="参数">
            <a-button style="width: 100%" @click="showCodeModal(3)"> ... </a-button>
          </a-form-item>
          <a-form-item label="窗口宽度">
            <a-input v-model:value="modalForm.width" placeholder="请输入" />
          </a-form-item>
          <a-form-item label="窗口高度">
            <a-input v-model:value="modalForm.height" placeholder="请输入" />
          </a-form-item>
          <div class="event_button">
            <a-button type="primary" style="width: 100%" @click="handleConfirmEventByModel">
              确认事件
            </a-button>
            <a-button style="width: 100%" @click="handleCancelEventByModel"> 清除事件 </a-button>
          </div>
        </a-form>
      </a-collapse-panel>
      <a-collapse-panel v-if="activePen?._isApi" key="绑定接口" header="绑定接口">
        <a-form
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          :colon="false"
          labelAlign="left"
          autocomplete="off"
        >
          <a-form-item label="接口URL">
            <a-input v-model:value="apiForm.url" placeholder="请输入" />
          </a-form-item>
          <a-form-item label="接口参数">
            <a-button style="width: 100%" @click="showCodeModal(2)"> ... </a-button>
          </a-form-item>
          <div class="event_button">
            <a-button type="primary" style="width: 100%" @click="handleConfirmEventByApi">
              确认事件
            </a-button>
            <a-button style="width: 100%" @click="handleCancelEventByApi"> 清除事件 </a-button>
          </div>
        </a-form>
      </a-collapse-panel>
    </a-collapse>

    <!-- <a-modal
      v-model:open="visible"
      title="Javascript"
      :width="800"
      wrapClassName="editor-modal"
      :bodyStyle="{ padding: 0 }"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div style="height: 400px">
        <json-editor v-model="javascriptJson" />
      </div>
    </a-modal> -->

    <CodeEditorModal
      ref="editorRef"
      :title="codeModalTitle"
      :show="codeModalVisible"
      @confirm="codeModalVisible = false"
      @cancel="codeModalVisible = false"
      @createTemplate="createTemplate"
      :activePen="activePen"
      :value="codeVal"
      @change="codeVal = $event"
      :createTemplateHelp="apiFormParamsExplain"
      :createTemplateBtnShow="codeModalType == 2"
      mode="json"
    />
  </div>
</template>

<script lang="ts">
  import CodeEditorModal from '/@process-editor/views/editor/components/PenProps/CodeEditorModal.vue';
  import {
    Collapse,
    CollapsePanel,
    Form,
    FormItem,
    Select,
    SelectOption,
    // Modal,
    TreeSelect,
  } from 'ant-design-vue';
  // import { PlusCircleOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import {
    bindFlowApi,
    bindEquipmentApi,
    getFlowDataListApi,
    getSenceGroupIndicatorTreeApi,
    getResourceInterfacePage,
    processFileUploadRemindingApi,
  } from '/@process-editor/api/index';
  // import { eventBehavior } from '/@process-editor/views/editor/data/defaultsConfig.js';
  import { useProcess } from '/@process-editor/hooks/useProcess';
  import { PLATFORM_INFO, MODEL_INFO, CANVAS_DATA } from '/@process-editor/constant/process';
  import { HUpload } from '/@/components/Upload';
  // import { isImgPen } from '/@process-editor/core/share';

  const ACollapse = Collapse;
  const ACollapsePanel = CollapsePanel;
  const AForm = Form;
  const AFormItem = FormItem;
  const ASelect = Select;
  const ASelectOption = SelectOption;
  // const AModal = Modal;

  export {};
</script>

<script setup lang="ts">
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getModelDetailByIsOldPen } from '/@process-editor/core/common';
  import { isImgPen, isOldPen } from '/@process-editor/core/share';
  import { eachTree } from '/@process-editor/views/editor/utils/index';
  import { EventAction } from 'hlxb-meta2d-core';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { DICT } from '/@/enums/dict';
  import { PenType } from 'hlxb-meta2d-core';
  import { DEVICE_CHANGE_SWITCH_API } from '/@process-editor/constant';
  import { FLOW_VERSION } from '/@process-editor/constant/process';
  import { saveFlowDataDetail as saveFlowData } from '/@process-editor/utils/index';

  const route = useRoute();

  const { getEditorLocalStorage } = useProcess();

  const { createMessage } = useMessage();

  const activeKey = ref();
  const flowForm = reactive({
    flowId: '',
    operation: '',
  });
  const operationOptions = [
    {
      label: '弹窗',
      value: 'modal',
    },
    {
      label: '导航',
      value: 'navigation',
    },
  ];
  const deviceForm = reactive({
    eqId: '',
  });

  const flowOptions = ref([]);
  const eqOptions = ref([]);
  const activePen = ref();
  let event = reactive({
    name: '',
    action: '',
    value: '',
  });
  let otherParams = [];

  const codeModalVisible = ref(false);
  const codeModalType = ref(2); //  2 接口 3弹窗
  const codeModalTitle = computed(() => {
    switch (codeModalType.value) {
      case 2:
        return '接口参数';
      case 3:
        return '参数';
    }
    return 'javascript';
  });
  const editorRef = ref(null);
  const codeVal = ref('');
  //弹窗参数
  const modalFormTemp = {
    title: '',
    route: '',
    params: '{}',
    width: '',
    height: '',
  };
  //弹窗参数
  const modalForm = ref({
    title: '',
    route: '',
    params: '{}',
    width: '',
    height: '',
  });

  onMounted(() => {
    meta2d.on('active', (pen) => {
      console.log(
        '%cpen===>499： ',
        'background: rgb(25, 197, 237,.6); color: #ff5025; font-size:18px;font-weight:700',
        pen,
      );

      if (pen.length === 1) {
        activePen.value = pen[0];
        setLineProps(activePen.value);
        if (!activePen.value.events) {
          activePen.value.events = [];
          datasetForm.groupCode = '';
          datasetForm.resourceInterfaceId = '';
          datasetForm.actionType = '';
          pipelineForm.ids = [];
          fileList.value = [];
        } else {
          // 有事件？
          const actEvent = activePen.value.events[0];

          if (actEvent) {
            event.name = actEvent.name;
            event.action = actEvent.action;
            otherParams.forEach((i) => (event[i] = actEvent[i]));
          } else {
            event.name = '';
            event.action = '';
            event.value = '';

            flowForm.flowId = '';
            deviceForm.eqId = '';
          }

          datasetForm.groupCode = activePen.value.groupCode ?? '';
          datasetForm.resourceInterfaceId = activePen.value.resourceInterfaceId ?? '';
          datasetForm.actionType = activePen.value.actionType ?? '';
          pipelineForm.ids = activePen.value.pipelineForm?.ids ?? [];
          fileList.value = activePen.value.pipelineForm?.fileList ?? [];
          apiForm.value = activePen.value.apiForm || {};
          if (!apiForm.value.url) apiForm.value.url = DEVICE_CHANGE_SWITCH_API;
          modalForm.value = activePen.value.modalForm || JSON.parse(JSON.stringify(modalFormTemp));
        }
        activeKey.value = [];
      } else {
        activePen.value = undefined;
      }
    });
  });
  function setLineProps(pen) {
    if (pen.type === PenType.Line) {
      pen._isModal = true;
    }
  }
  const show = ref(false);
  function onCollapseChange(keys) {
    const modelInfo = getEditorLocalStorage(MODEL_INFO);
    const meta2dData = getEditorLocalStorage(CANVAS_DATA);
    const pens = meta2dData.pens || [];
    const isOld = isOldPen(activePen.value, pens);
    if (keys.includes('绑定设备流水线')) {
      const flowId = modelInfo.showDetail || '';
      const operation = modelInfo.operation || '';
      flowForm.flowId = isOld ? flowId : '';
      flowForm.operation = isOld ? operation : '';
    }

    if (keys.includes('绑定设备')) {
      const eqId = modelInfo.bindEquipmentId || '';
      deviceForm.eqId = isOld ? eqId : '';
    }

    if (keys.includes('绑定管线')) {
      getPipelineList();
      setTimeout(() => {
        show.value = true;
      }, 3000);
    }
  }

  async function confirmFlow() {
    const meta2dData = getEditorLocalStorage(CANVAS_DATA);
    const pens = meta2dData.pens || [];
    const modelInfo = await getModelDetailByIsOldPen(activePen.value, pens, route);
    if (!flowForm.flowId) {
      createMessage.info('请选择设备流水线');
      return;
    } else if (!flowForm.operation) {
      createMessage.info('请选择行为类型');
      return;
    }

    const params = {
      aimFlowCode: flowForm.flowId,
      detailModelId: modelInfo.id,
      operation: flowForm.operation,
    };
    await bindFlowApi(params);
    await saveFlowDataDetail();
    createMessage.success('绑定流程图成功');
  }

  async function removeFlow() {
    flowForm.flowId = '';
    handleFlowChange('');

    const modelInfo = getEditorLocalStorage(MODEL_INFO);
    const params = {
      aimFlowCode: flowForm.flowId,
      detailModelId: modelInfo.id,
    };
    await bindFlowApi(params);
    await saveFlowDataDetail();
    createMessage.success('解绑流程图成功');
  }

  function filterOption(input, option) {
    return option.key.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }

  async function confirmDevice() {
    const meta2dData = getEditorLocalStorage(CANVAS_DATA);
    const pens = meta2dData.pens || [];
    const modelInfo = await getModelDetailByIsOldPen(activePen.value, pens, route);
    if (!deviceForm.eqId) {
      createMessage.info('请选择设备');
      return;
    }
    const params = {
      eqId: deviceForm.eqId,
      modelId: modelInfo.id,
    };
    await bindEquipmentApi(params);
    await saveFlowDataDetail();
    createMessage.success('绑定设备成功');
  }

  async function removeDevice() {
    deviceForm.eqId = '';
    handleDeviceChange('');
    const modelInfo = getEditorLocalStorage(MODEL_INFO);
    const params = {
      EqId: deviceForm.eqId,
      ModelId: modelInfo.Id,
    };
    await bindEquipmentApi(params);
    await saveFlowDataDetail();
    createMessage.success('解绑设备成功');
  }

  async function saveFlowDataDetail() {
    await saveFlowData({
      flowId: route.query.flowId,
      version: getEditorLocalStorage(FLOW_VERSION),
    });
  }

  // const visible = ref(false);
  // const javascriptJson = ref('');

  // function handleOk() {
  //   visible.value = false;
  // }
  // function handleCancel() {
  //   visible.value = false;
  //   javascriptJson.value = '';
  // }

  async function getFlowData() {
    const platformInfo = getEditorLocalStorage(PLATFORM_INFO);
    const params = {
      bindSourceFrom: platformInfo?.bindSourceFrom || route.query?.bindSourceFrom,
      bindSourceUniqueId: platformInfo?.bindSourceUniqueId || route.query?.bindSourceUniqueId,
    };

    const res = await getFlowDataListApi(params);
    flowOptions.value =
      res
        .filter((i) => i.id !== route.query.flowId)
        .map((j) => ({
          label: j.flowName,
          value: j.id,
        })) || [];
  }

  // async function getAllEqData() {
  //   try {
  //     const res = await getAllEqApi(route.query.bindSourceUniqueId);
  //     eqOptions.value = (res || []).map((i) => ({
  //       label: i.Name,
  //       value: i.Id,
  //     }));
  //   } catch (error) {
  //     console.log(error);
  //   }
  // }
  /* 获取路由字典数组 */
  const routeOptions = ref([]);
  async function getRouteOptions() {
    routeOptions.value = await getDictTypeListApi({ type: DICT.PROCESS_MODAL_ROUTE });
  }
  function getData() {
    getFlowData();
    // getAllEqData();
    getRouteOptions();
  }

  getData();

  function handleFlowChange(e) {
    meta2d.setValue({
      id: activePen.value.id,
      showDetail: e,
    });
  }

  function handleOperationChange(e) {
    meta2d.setValue({
      id: activePen.value.id,
      operation: e,
    });
  }

  function handleDeviceChange(e) {
    meta2d.setValue({
      id: activePen.value.id,
      bindEquipmentId: e,
    });
  }

  const datasetForm = reactive({
    groupCode: '',
    resourceInterfaceId: '',
    actionType: '',
  });

  const activeTypeOptions = [
    {
      label: '点击',
      value: 'click',
    },
    {
      label: '移入',
      value: 'enter',
    },
  ];
  const treeData = ref([]);
  async function getIndexGroupData() {
    const data = await getSenceGroupIndicatorTreeApi();
    eachTree(data, (item) => {
      if (item.children) {
        item.disabled = true;
      }
      if (!item.hasOwnProperty('groupCode')) {
        item.groupCode = item.value;
      }
    });
    treeData.value = data;
  }

  function onTreeSelectChange() {
    const param = {
      id: activePen.value.id,
      groupCode: datasetForm.groupCode,
      height: activePen.value.fontSize,
    };

    updateRender(param);
  }

  const interfaceOptions = ref([]);
  async function getInterfaceList() {
    const params = {
      current: 1,
      pageSize: 999,
      resourceIndexId: 1,
    };
    const data = await getResourceInterfacePage(params);
    interfaceOptions.value = (data.records || []).map((i) => ({
      label: i.interfaceName,
      value: i.id,
    }));
  }

  function handleInterfaceChange() {
    updateRender({
      id: activePen.value.id,
      resourceInterfaceId: datasetForm.resourceInterfaceId,
      height: activePen.value.fontSize,
    });
  }

  function handleActionTypeChange() {
    updateRender({
      id: activePen.value.id,
      actionType: datasetForm.actionType,
      height: activePen.value.fontSize,
    });
  }

  function updateRender(param) {
    if (activePen.value.type === 1) {
      delete param.height;
    }
    meta2d.setValue(param);
    meta2d.canvas.calcActiveRect();
    meta2d.render();
  }
  function getDatasetData() {
    getIndexGroupData();
    getInterfaceList();
  }

  getDatasetData();

  async function handleConfirmEventByDataset() {
    if (!datasetForm.resourceInterfaceId) {
      createMessage.info('请选择接口');
      return;
    } else if (!datasetForm.groupCode) {
      createMessage.info('请选择数据集');
      return;
    } else if (!datasetForm.actionType) {
      createMessage.info('请选择行为类型');
      return;
    }

    const params = {
      id: activePen.value.id,
      events: [
        {
          action: EventAction.Emit,
          fn: null,
          name: datasetForm.actionType,
          value: 'pen-enter',
          params: '鼠标移入事件',
          where: { type: null },
        },
      ],
    };

    meta2d.setValue(params);

    await saveFlowDataDetail();
    createMessage.success('绑定数据集成功');
  }

  async function handleCancelEventByDataset() {
    updateRender({
      id: activePen.value.id,
      groupCode: '',
      resourceInterfaceId: '',
      height: activePen.value.fontSize * 1.5,
      events: [],
    });

    datasetForm.groupCode = '';
    datasetForm.resourceInterfaceId = '';
    await saveFlowDataDetail();
    createMessage.success('解绑数据集成功');
  }

  const pipelineOptions = ref([]);
  function getPipelineList() {
    const data = window.meta2d.data();
    const pens = data.pens || [];
    pipelineOptions.value = pens
      .filter((i) => i.name === 'line')
      .map((i) => ({
        label: `管线-${i.id}`,
        value: i.id,
      }));
  }

  const fileList = ref([]);
  const pipelineForm = reactive({
    ids: [],
  });
  const apiForm = ref({
    url: DEVICE_CHANGE_SWITCH_API,
    params: '{}',
    disabled: false,
  });
  function handlePipelineChange(ids) {
    const formData = {
      ids,
      fileList: fileList.value,
    };
    meta2d.setValue({
      id: activePen.value.id,
      pipelineForm: formData,
    });
  }

  async function handleUploadChange([file]) {
    const formData = {
      ids: pipelineForm.ids,
      fileList: [{ url: file?.url, fileName: file?.url }],
    };
    // isImgPen()
    meta2d.setValue({
      id: activePen.value.id,
      pipelineForm: formData,
    });

    // const pen = meta2d.data().pens;
  }

  async function handleConfirmEventByApi() {
    if (!apiForm.value.url) {
      createMessage.info('请输入接口URL');
      return;
    }
    apiForm.value.disabled = false;
    meta2d.setValue({
      id: activePen.value.id,
      apiForm: apiForm.value,
      _pointer: true,
    });
    await saveFlowDataDetail();
    createMessage.success('绑定接口成功');
  }

  async function handleCancelEventByApi() {
    apiForm.value.url = DEVICE_CHANGE_SWITCH_API;
    apiForm.value.params = '{}';
    apiForm.value.disabled = true;
    meta2d.setValue({
      id: activePen.value.id,
      apiForm: apiForm.value,
      _pointer: false,
    });

    await saveFlowDataDetail();
    createMessage.success('解绑接口成功');
  }
  async function handleConfirmEventByPipeline() {
    // if (!pipelineForm.ids.length) {
    //   createMessage.info('请选关联管线');
    //   return;
    // }

    const formData = {
      fileList: fileList.value,
      image: activePen.value.image,
      ...pipelineForm,
    };

    meta2d.setValue({
      id: activePen.value.id,
      _pointer: true,
      events: [
        {
          action: EventAction.Emit,
          fn: null,
          name: 'click',
          value: 'valve-click',
          params: '鼠标点击事件',
          where: { type: null },
        },
      ],
      pipelineForm: formData,
    });

    await saveFlowDataDetail();
    createMessage.success('绑定管线成功');
  }

  async function handleCancelEventByPipeline() {
    meta2d.setValue({
      id: activePen.value.id,
      _pointer: false,
      pipelineForm: {
        ids: [],
        fileList: [],
      },
    });

    pipelineForm.ids = [];
    fileList.value = [];
    await saveFlowDataDetail();
    createMessage.success('解绑管线成功');
  }

  async function handleConfirmEventByModel() {
    if (!modalForm.value.title) {
      createMessage.info('请输入弹窗标题');
      return;
    }

    meta2d.setValue({
      id: activePen.value.id,
      modalForm: modalForm.value,
      _pointer: true,
    });

    await saveFlowDataDetail();
    createMessage.success('绑定弹窗成功');
  }
  async function handleCancelEventByModel() {
    let obj = JSON.parse(JSON.stringify(modalFormTemp));
    meta2d.setValue({
      id: activePen.value.id,
      modalForm: obj,
      _pointer: false,
    });

    modalForm.value = obj;
    await saveFlowDataDetail();
    createMessage.success('解绑弹窗成功');
  }
  const onCodeChange = (v) => {
    if (codeModalType.value == 2) apiForm.value.params = v;
    else if (codeModalType.value == 3) modalForm.value.params = v;
  };
  /*
   * 打开JSON参数弹框
   * @property {string} v = 类型
   * 	@value == 2接口
   * 	@value == 3弹窗
   */
  watch(() => codeVal.value, onCodeChange);
  const showCodeModal = (v) => {
    codeModalType.value = v;
    let str = '';
    if (v == 2) str = apiForm.value.params || '';
    else if (v == 3) str = modalForm.value.params || '';
    codeVal.value = str;
    codeModalVisible.value = true;
    setTimeout(() => {
      editorRef.value && editorRef.value.setValue(str);
    }, 100);
  };
  activePen.value;
  const apiFormParamsExplain = computed(() => {
    if (activePen.value && isImgPen(activePen.value)) {
      return [
        '属性说明：',
        'checkVal  要选中的值',
        'unCheckVal 未选中的值',
        'indicatorCodes 指标数组',
        'pipleFlows 管线数组 [true,false] 第一项为unCheckVal值的管线流动状态，第二项为checkVal值的管线流动状态',
        '示例{"checkVal":"1","unCheckVal":"0","indicatorCodes":["LXJ_1#_ON"],"pipleFlows":[false,true]}',
      ];
    }

    return [
      '属性说明：',
      'changeVal 要设置的值',
      'indicatorCodes 指标数组',
      'afterFetchPipelineFlow 接口发送后已绑管线是否流动',
      'afterSuccessParams 接口发送后的参数',
      '示例{"changeVal":"1","indicatorCodes":["LXJ_1#_ON"],"afterFetchPipelineFlow":true',
    ];
  });

  function createTemplate() {
    const indicatorCodes = meta2d.findOne(activePen.value.id)?.dataForm?.indicatorCodes;
    console.log(
      '%cindicatorCodes===>965： ',
      'background: rgb(25, 197, 237,.6); color: #ff5025; font-size:18px;font-weight:700',
      indicatorCodes,
    );

    let obj: Indexable = {
      changeVal: '',
      indicatorCodes: [],
    };
    if (isImgPen(activePen.value)) {
      obj = {
        checkVal: '1',
        unCheckVal: '0',
        indicatorCodes: [],
        pipleFlows: [false, true],
      };
    }
    if (indicatorCodes?.length) {
      obj.indicatorCodes = [indicatorCodes[0]?.indicatorCode];
    }
    if (!editorRef.value) return;
    editorRef.value.setValue(JSON.stringify(obj));
  }
</script>

<style lang="less" scoped>
  .event {
    // margin: 16px;

    :deep(.ant-form) {
      // padding: 16px 12px;

      .ant-form-item {
        .property {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 12px;
        }
      }
    }

    .event_button {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 16px;
    }
  }
</style>

<style lang="less" scoped>
  // :global(.ant-modal-body) {
  //   padding: 0;
  // }
</style>
