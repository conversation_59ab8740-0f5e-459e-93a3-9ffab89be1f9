// 定义表单 schema
export const timeUnitOptions = [
  { label: '秒', value: 'second', multiple: 1 },
  { label: '分', value: 'minute', multiple: 60 },
  { label: '时', value: 'hour', multiple: 3600 },
  { label: '天', value: 'day', multiple: 86400 },
];

export const schemas = [
  {
    field: 'intervalTimeTotal',
    label: '数据间隔',
    component: 'InputNumber',
    required: true,
    componentProps: {
      style: 'width: 100%',
    },
  },
  {
    field: 'intervalTimeUnit',
    label: '数据间隔单位',
    component: 'Select',
    required: true,
    componentProps: {
      style: 'width: 100%',
      options: timeUnitOptions,
      popupClassName: 'control-setting-modal-select-dropdown aoa3',
    },
  },
  {
    field: 'stopTimeTotal',
    label: '结束时间',
    required: true,
    component: 'InputNumber',
    componentProps: {
      style: 'width: 100%',
    },
  },
  {
    field: 'stopTimeUnit',
    label: '结束时间单位',
    required: true,
    component: 'Select',
    componentProps: {
      style: 'width: 100%',
      options: timeUnitOptions,
      popupClassName: 'control-setting-modal-select-dropdown aoa3',
    },
  },
];
