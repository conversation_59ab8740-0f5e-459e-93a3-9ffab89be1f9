<template>
  <BiCardBox title="吨水单耗" direction="right">
    <template #content>
      <div class="electricity-content w-full h-full">
        <div ref="chartRef" class="chart w-full h-full"></div>
      </div>
    </template>
  </BiCardBox>
</template>

<script lang="ts" setup>
  import { ref, PropType, watch } from 'vue';
  import { BiCardBox } from './BiCard';
  import { useECharts } from '/@/hooks/web/useECharts';
  import type { ChartData } from '../type';
  import { getScaleValByClientWidth } from '/@aoa/views/cockpit/data';
  import { lineChartIcon1, lineChartIcon2 } from '/@aoa/views/BI/data';
  import dayjs from 'dayjs';

  const props = defineProps({
    data: {
      type: Array as PropType<ChartData[]>,
      default: () => [],
    },
    first: Boolean,
  });

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef as any);
  const colorList = [
    ['rgba(252, 208, 86, 1)', 'rgba(252, 208, 86, 0.4)', 'rgba(252, 208, 86, 0)'],
    ['rgba(44, 255, 241, 1)', 'rgba(44, 255, 241, 0.4)', 'rgba(44, 255, 241, 0)'],
  ];

  // const dataList = ['', '-环比'];
  const dataList = [''];

  const getLendData = (data) => {
    if (!data) return [];
    return dataList.map((_, index) => (index > 0 ? data.name1 : data.name));
  };

  const getXAxisData = (data: ChartData[]) => {
    return data.map((item) => item.collectDateTime);
  };

  const getMax = (data: ChartData[]) => {
    console.log(data);
    // const dataArr = data
    //   .map((item) => [item.value, item.value1])
    //   .flat()
    //   .filter((i) => i !== '' && i !== undefined && i !== null);

    // return dataArr.length <= 0 ? 1 : Math.max(...dataArr);
    return 0.5;
  };

  const getMin = (data: ChartData[]) => {
    console.log(data);
    // const dataArr = data
    //   .map((item) => [item.value, item.value1])
    //   .flat()
    //   .filter((i) => i !== '' && i !== undefined && i !== null);
    // return dataArr.length <= 0 ? 0 : Math.min(...dataArr);
    return 0;
  };

  const getValueFormatter = (value, digit = 2) => {
    return Number(value).toFixed(digit);
  };

  const getSeriesData = (data: ChartData[]) => {
    return dataList.map((_, index) => {
      return {
        name: index > 0 ? data[0]?.name1 : data[0]?.name,
        type: 'line',
        data: data.map((item) => (index > 0 ? item.value1 : item.value)),
        symbol: index % 2 ? lineChartIcon1 : lineChartIcon2,
        symbolSize: 13,
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: colorList[index][1],
              },
              {
                offset: 1,
                color: colorList[index][2],
              },
            ],
            global: false,
          },
        },
        lineStyle: {
          color: colorList[index][0],
        },
        itemStyle: {
          color: colorList[index][0],
        },
      };
    });
  };

  const setChart = () => {
    const { data } = props;

    let max = getMax(data);
    let min = getMin(data);

    const options = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#E1F3F1',
        borderColor: '#02695E',
        appendToBody: true,
        formatter: (params) => {
          return `<span style="font-size: 0.73vw; line-height: 1.5;">${dayjs(
            params[0].axisValue,
          ).format('YYYY-MM-DD')}</span></br>
          ${params
            .map((item, index) => {
              return `
            ${item.marker}&nbsp;<span style="font-size: 0.73vw; line-height: 1.5;">${
                item.seriesName
              }</span>&nbsp;<span >${
                item.value !== '' && item.value !== null && item.value !== undefined
                  ? `<span style="font-weight: 600;font-size: 0.73vw; line-height: 1.5;">${getValueFormatter(
                      item.value,
                      data[index].digit,
                    )} </span>&nbsp;<span style="font-weight: 600;font-size: 0.73vw; line-height: 1.5;">${
                      data[index]?.unit
                    }</span>`
                  : '-'
              }</span>&nbsp;
            `;
            })
            .join('</br>')}
          `;
        },
        // valueFormatter: (value) => {
        //   return value !== '' && value !== null && value !== undefined
        //     ? `${getValueFormatter(value, data[0].digit)} ${data[0]?.unit}`
        //     : '-';
        // },
      },
      legend: {
        show: false,
        data: getLendData(data[0]),
        top: 0,
        right: 0,
        itemWidth: getScaleValByClientWidth(8),
        itemHeight: getScaleValByClientWidth(8),
        itemGap: 8,
        textStyle: {
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          lineHeight: getScaleValByClientWidth(13 * 1.25),
        },
        lineStyle: {
          width: 0,
          inactiveWidth: 0,
        },
      },
      grid: {
        left: 8,
        right: 8,
        bottom: 0,
        top: getScaleValByClientWidth(35),
        containLabel: true,
        show: true,
        backgroundColor: 'rgba(2, 53, 32, 0.40)',
        borderWidth: 0,
      },
      xAxis: {
        type: 'category',
        data: getXAxisData(data),
        boundaryGap: false,
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          padding: [0, 10, 0, 0],
          formatter: (value) => {
            return Number(dayjs(value).format('DD')) + '日';
          },
        },
        axisTick: {
          show: false,
        },
        axisPointer: {
          show: true,
          type: 'line',
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            type: [5, 10],
          },
        },
        axisLine: {
          show: true,
          // lineStyle: {
          //   color: 'rgba(178, 255, 241, 1)',
          // },
          lineStyle: {
            color: 'rgba(178, 255, 241, 1)',
            shadowColor: 'rgba(178, 255, 241, 1)',
            shadowOffsetY: -2,
            shadowOffsetX: 0,
            shadowBlur: 5,
            width: 1,
          },
        },
      },
      yAxis: {
        type: 'value',
        name: data[0]?.unit ? `吨水单耗(${data[0].unit})` : '',
        max,
        min,
        interval: (max - min) / 5,
        nameTextStyle: {
          fontSize: getScaleValByClientWidth(13),
          color: '#fff',
          align: 'left',
          padding: [0, 0, 0, getScaleValByClientWidth(-25)],
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: getScaleValByClientWidth(13),
          formatter: (value) => getValueFormatter(value, 1),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A1D0C4',
            type: [5, 10],
            offset: 5,
          },
        },
      },
      series: getSeriesData(data),
    };

    setOptions(options as any, false);
  };

  watch(
    () => props.first,
    () => {
      setChart();
    },
    { deep: true },
  );
</script>
<style lang="less" scoped>
  .electricity-content {
    @media screen and (min-width: 1800px) {
      .px2vw(6);
      padding: 0 @vw @vw @vw;
    }
  }
</style>
