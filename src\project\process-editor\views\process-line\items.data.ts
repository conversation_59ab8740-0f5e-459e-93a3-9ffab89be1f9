import { h } from 'vue';
import { pick } from 'lodash-es';
import { Switch } from 'ant-design-vue';
import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';
import { getFactoryListApi } from '/@/api/process';
import { addOrUpdateFlowDataApi } from '/@/api/process';
import dayjs from 'dayjs';
import { getDictTypeListApi } from '/@/api/admin/dict';
import { DICT } from '/@/enums/dict';

export const columns: BasicColumn[] = [
  {
    title: '流水线名称',
    dataIndex: 'flowName',
    // width: 220,
  },
  {
    title: '水厂',
    dataIndex: 'bindSourceUniqueId',
    // width: 220,
  },
  // {
  //   title: '创建人',
  //   dataIndex: 'createBy',
  // },
  // {
  //   title: '创建时间',
  //   dataIndex: 'createTime',
  // },

  // {
  //   title: '更新人',
  //   dataIndex: 'updateBy',
  // },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    customRender: ({ record }) => {
      return dayjs(record.updateTime).format('YYYY-MM-DD HH:mm');
    },
    // width: 220,
  },
  {
    title: '备注',
    dataIndex: 'description',
  },
  {
    title: '中控室显示',
    dataIndex: 'isShow',
    customRender: ({ record }) => {
      if (!Reflect.has(record, 'pendingStatus')) {
        record.pendingStatus = false;
      }
      return h(Switch, {
        checked: Boolean(record.isShow),
        loading: record.isPending,
        onChange() {
          record.isPending = true;
          const { createMessage } = useMessage();
          const params = pick(record, [
            'id',
            'recentVersion',
            'isShow',
            'flowName',
            'bindSourceUniqueId',
            'isShow',
            'sortNumber',
          ]);
          params.isShow = params.isShow ? 0 : 1;
          addOrUpdateFlowDataApi(params)
            .then(() => {
              record.isShow = record.isShow ? 0 : 1;
              createMessage.success(`修改成功`);
            })
            .catch(() => {
              createMessage.error('修改失败');
            })
            .finally(() => {
              record.isPending = false;
            });
        },
      });
    },
    // width: 220,
  },
  {
    title: '更新人',
    dataIndex: 'updateName',
  },
  {
    title: '排序',
    dataIndex: 'sortNumber',
    // width: 220,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'bindSourceUniqueIds',
    label: '水厂',
    labelWidth: 40,
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps() {
      return {
        api: getFactoryListApi,
        allowClear: false,
        mode: 'multiple',
        labelField: 'name',
        valueField: 'id',
      };
    },
  },
  {
    field: 'flowName',
    label: '搜索内容',
    component: 'Input',
    componentProps: {},
    colProps: { span: 6 },
  },
];

export const modalFormSchemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: '流程Id',
    show: false,
    colProps: {
      span: 24,
    },
    componentProps: {},
  },
  {
    field: 'recentVersion',
    component: 'Input',
    label: '版本',
    show: false,
    colProps: {
      span: 24,
    },
    componentProps: {},
  },
  {
    required: true,
    field: 'flowName',
    component: 'Input',
    label: '流水线名称',
    colProps: {
      span: 24,
    },
    componentProps: {},
  },
  {
    required: true,
    field: 'bindSourceUniqueId',
    label: '水厂',
    component: 'Select',
    colProps: { span: 24 },
    componentProps() {
      return {
        options: [],
      };
    },
  },
  {
    field: 'sortNumber',
    component: 'InputNumber',
    label: '排序',
    colProps: {
      span: 24,
    },
    componentProps: {},
  },
  {
    field: 'pipelineType',
    component: 'ApiSelect',
    label: '流水线分类',
    colProps: {
      span: 24,
    },
    componentProps: {
      api: getDictTypeListApi,
      params: { type: DICT.PIPELINE_TYPE },
      allowClear: true,
    },
  },
  {
    required: true,
    field: 'isShow',
    label: '中控室显示',
    component: 'Switch',
    defaultValue: 1,
    componentProps: {
      checkedValue: 1,
      unCheckedValue: 0,
    },
    colProps: {
      span: 24,
    },
  },

  {
    field: 'description',
    label: '备注',
    component: 'InputTextArea',
    defaultValue: '',
    componentProps: {},
    colProps: {
      span: 24,
    },
  },
];
