import { useDrawer } from '/@/components/Drawer';
import { provide, inject, computed, unref } from 'vue';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { SettingButtonPositionEnum } from '/@/enums/appEnum';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';

export const useSettingConfig = () => {
  const getShowSetting = computed(() => {
    const userStore = useUserStoreWithOut();
    const { getShowHeader } = useHeaderSetting();
    const { getShowSettingButton, getSettingButtonPosition } = useRootSetting();
    if (
      (userStore.getUserInfo.userFlag !== 1 || !unref(getShowSettingButton)) &&
      !userStore?.getRoleList?.includes('4825')
    ) {
      return false;
    }
    const settingButtonPosition = unref(getSettingButtonPosition);

    if (settingButtonPosition === SettingButtonPositionEnum.AUTO) {
      return unref(getShowHeader);
    }
    return settingButtonPosition === SettingButtonPositionEnum.HEADER;
  });

  return {
    getShowSetting,
  };
};

export const useSettingDrawer = () => {
  const [register, { openDrawer, closeDrawer }] = useDrawer();

  provide('openSettingDrawer', openDrawer);

  return {
    registerSettingDrawer: register,
    closeSettingDrawer: closeDrawer,
  };
};

export const useSettingDrawerMethod = () => {
  const openSettingDrawer = inject<() => void>('openSettingDrawer')!;

  return {
    openSettingDrawer,
  };
};
