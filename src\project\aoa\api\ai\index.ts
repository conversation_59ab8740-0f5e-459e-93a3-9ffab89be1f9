import { defZhczHttp } from '/@/utils/http/axios';
import { streamFetch } from '/@/utils/http/fetchEventSource/fetchEventSource';
import { type StreamFetchProps } from '/@/utils/http/fetchEventSource/types/fetch';

enum Api {
  AIResult = '/safe/deepseek/analyze',
  AIStreamResult = '/safe/api/deepseek-stream',
}

// 获取ai分析数据
export const getAiAnalysisApi = (params: any) =>
  defZhczHttp.post<any>({
    url: Api.AIResult,
    params,
    timeout: 300 * 1000,
  });

// 获取ai分析流式数据
export const getStreamAiAnalysisApi = ({ params, onMessage, abortCtrl }: StreamFetchProps) =>
  streamFetch({ url: Api.AIStreamResult, params, onMessage, abortCtrl });
