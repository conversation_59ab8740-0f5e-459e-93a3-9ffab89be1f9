<template>
  <span
    :class="`${prefixCls}__extra-favorites`"
    :style="{ color: findFavoritesPage() ? '#FC7C22' : '', display: 'none' }"
    @click="addOrDeleteFavorites"
  >
    <IconButton tooltip="收藏" :icon="getIcon" :size="16" hoverSize="100%" hoverColor="none" />
  </span>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useZHCZMenuStore } from '/@zhcz/store/modules/menu';
  // import { useUserStore } from '/@/store/modules/user';
  // import { createLocalStorage } from '/@/utils/cache';
  // import { FACTORY_KEY } from '/@/enums/cacheEnum';
  import { IconButton } from '/@/components/Button';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDesign } from '/@/hooks/web/useDesign';
  // import { usePermission } from '/@/hooks/web/usePermission';
  import { getFavoritesList, addFavorites, deleteFavorites } from '/@zhcz/api/admin/menu';
  import type { QueryAddFavoritesListParams } from '/@zhcz/api/admin/model/menu';
  // import { getCurrentUserFactoryList } from '/@zhcz/api/patrol';

  const { prefixCls } = useDesign('multiple-tabs-content');

  const router = useRouter();
  const { currentRoute } = router;

  const { createMessage } = useMessage();

  const zhczMenuStore = useZHCZMenuStore();
  const favoritesMenus = computed(() => zhczMenuStore.getFavoritesMenus);

  function findFavoritesPage() {
    return favoritesMenus.value.find((item) => item.path === currentRoute.value.path);
  }

  const getIcon = computed(() => {
    return findFavoritesPage() ? 'icon-park-solid:star' : 'icon-park-outline:star';
  });

  // 收藏或取消收藏
  async function addOrDeleteFavorites() {
    const page = findFavoritesPage();
    if (page) {
      await deleteFavorites(page.id);
      createMessage.success('取消收藏成功');
    } else {
      // 收藏
      const params: QueryAddFavoritesListParams = {
        name: currentRoute.value.meta.title,
        path: currentRoute.value.path,
      };
      await addFavorites(params);
      createMessage.success('收藏成功');
    }
    const favoritesList = await getFavoritesList();
    zhczMenuStore.setFavoritesMenus(favoritesList);
  }

  // const userStore = useUserStore();
  // const ls = createLocalStorage();
  // const factoryId = computed(() => ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId);
  // const { permissionStore } = usePermission();
  // const getFactoryList = async () => {
  //   const moduleId = permissionStore.getFirstMenuParams?.moduleId;
  //   const res = await getCurrentUserFactoryList({ moduleId });
  //   if (res?.bindSourceUniqueId) {
  //     // 匹配是否启用
  //     let setId = '-1';
  //     // const findIndex = res.factoryInfoList.findIndex((item) => item.factoryId === factoryId.value);
  //     const factoryInfoList = res.factoryInfoList.filter(
  //       (item) => item.factoryId === res.bindSourceUniqueId,
  //     );
  //     setId = res.bindSourceUniqueId || factoryId.value || setId;
  //     userStore.setFactoryId(setId);
  //     userStore.setUserInfo({
  //       ...userStore.getUserInfo,
  //       factoryInfoList: factoryInfoList,
  //       bindSourceUniqueId: setId,
  //     });
  //   } else {
  //     if (!factoryId.value) {
  //       userStore.setFactoryId('-1');
  //       userStore.setGLobalSource({ factoryId: '-1' });
  //     }
  //   }
  // };
  // watch(
  //   () => permissionStore.getFirstMenuParams?.moduleId,
  //   async (newData, oldData) => {
  //     if (newData !== oldData) {
  //       await getFactoryList();
  //     }
  //   },
  //   {
  //     deep: true,
  //     immediate: true,
  //   },
  // );
</script>

<style lang="less" scoped></style>
