import { ref, nextTick } from 'vue';
import { treeToList } from '/@process-editor/views/editor/utils/index';
import { getAddExpressionData } from '../utils';

export default function useSelectCode(props, treeData, updateFormDataByProp) {
  const availableCodeOptions = ref([]);

  // 更新可用的code选项，删除、code改动、树选择、初始化 触发
  async function updateAvailableCodeOptions() {
    const { resolve, promise: p } = Promise.withResolvers();
    nextTick(() => {
      resolve(null);
    });
    await p;

    if (!treeData.value || treeData.value.length === 0) return;

    const nodeList = treeToList(treeData.value);
    const selectedIds = props.data.businessData.tree || [];
    const nodes = nodeList.filter((i) => selectedIds.includes(i.value));
    const _nodes_ = nodes.map((i) => i.children || i).flat(Infinity);
    const indicators = _nodes_
      .map((i) =>
        i.indicators
          ? i.indicators.map((j) => ({
              ...j,
              groupCode: i.groupCode,
              resourceInterfaceId: i.defaultInterfaceId,
            }))
          : [],
      )
      .flat();

    // 获取已经选择的code
    const usedCodes = props.data.businessData.displayProductDataInfos.map((item) => item.code);

    // 过滤掉已经选择的code
    const availableCodes = indicators.filter((item) => !usedCodes.includes(item.code));

    // 转换为选择框需要的格式
    availableCodeOptions.value = availableCodes.map((item) => ({
      label: `${item.name} (${item.code})`,
      value: item.code,
      data: item,
    }));
  }

  // 处理code选择变化
  function onCodeSelectChange(value, expressionIndex) {
    if (!value) return;

    // 找到选中的code对应的数据
    const selectedOption = availableCodeOptions.value.find((item) => item.value === value);
    if (!selectedOption) return;
    const codeData = selectedOption.data;
    // 更新到displayProductDataInfos
    const addItem = getAddExpressionData(codeData);
    updateFormDataByProp((data) => {
      Object.assign(data.businessData.displayProductDataInfos[expressionIndex], addItem);
    });

    // 更新可用选项
    updateAvailableCodeOptions();
  }

  return { updateAvailableCodeOptions, onCodeSelectChange, availableCodeOptions };
}
