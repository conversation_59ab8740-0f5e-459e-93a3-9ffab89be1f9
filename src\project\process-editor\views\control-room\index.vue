<template>
  <div class="w-full h-full">
    <div class="control-main" :style="getStyle" ref="containerRef">
      <div class="tab-box">
        <a-row style="height: 100%; overflow: hidden">
          <a-col span="24" style="height: 100%; overflow: hidden">
            <div class="flow-swiper-container">
              <div class="cursor-pointer flex-shrink-0" @click="handlePrev" v-show="showNavigation">
                <Icon
                  icon="ant-design:left-outlined"
                  :color="swiperDisableBegin ? '#858585' : '#fff'"
                  :size="16"
                />
              </div>
              <Swiper
                ref="swiperRef"
                slidesPerView="auto"
                :spaceBetween="0"
                class="flex-1"
                @slide-change="handleSideChange"
              >
                <SwiperSlide v-for="(menu, index) in tabs" :key="index">
                  <div
                    v-if="!menu.pipelineType"
                    :key="index"
                    :class="['tab', { 'tab-actived': menu.id === checkedMenuId }]"
                    @click="switchTab(menu)"
                  >
                    <div class="name">{{ menu.flowName }}</div>
                  </div>
                  <Dropdown
                    v-else
                    class="custom-select-container"
                    overlayClassName="options-box"
                    :class="{ 'dropdown-active': menu.dropdownActive }"
                  >
                    <div @click.prevent>
                      {{ menu.pipelineName }}
                      <DownOutlined />
                    </div>
                    <template #overlay>
                      <Menu>
                        <MenuItem
                          v-for="option in menu.children"
                          :key="option.id"
                          :class="{ menu_actived: option.id === checkedMenuId }"
                          @click="handleSelectMenu(option)"
                        >
                          <span>
                            {{ option.flowName }}
                          </span>
                        </MenuItem>
                      </Menu>
                    </template>
                  </Dropdown>
                </SwiperSlide>
              </Swiper>
              <div
                class="cursor-pointer flex-shrink-0"
                :data-index="currentIndex"
                @click="handleNext"
                v-show="showNavigation"
              >
                <Icon
                  icon="ant-design:right-outlined"
                  :color="swiperDisableEnd ? '#858585' : '#fff'"
                  :size="16"
                />
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
      <div class="flex-1">
        <ProcessDiagram :flowId="flowId" v-if="flowId" @navigation-event="handleNavigationEvent" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick, unref } from 'vue';
  import { Row, Col, Dropdown, Menu, MenuItem } from 'ant-design-vue';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Icon } from '/@/components/Icon';
  import ProcessDiagram from '/@process-editor/components/BasicProcessDiagram/index.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { getFlowDataListApi } from '../../api/index';
  import { getFactoryId } from '/@process-editor/utils';
  import { getDictTypeListApi } from '/@/api/admin/dict';
  import { DICT } from '/@/enums/dict';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { useRouter } from 'vue-router';

  defineOptions({
    name: 'ControlRoom',
  });

  const userStore = useUserStore();
  const userInfo = computed(() => userStore.getUserInfo);
  const bindSourceFrom = computed(() => userInfo.value.bindSourceFrom);
  const bindSourceUniqueId = getFactoryId();

  const ARow = Row;
  const ACol = Col;

  const containerRef = ref();

  function handleSelectMenu(menu) {
    tabs.value
      .filter((item) => item.pipelineType)
      .forEach((item) => {
        item.dropdownActive = Number(item.pipelineType) === menu.pipelineType;
      });
    checkedMenuId.value = menu.id;
  }

  const swiperRef = ref();
  const currentIndex = ref(0);
  function handleSideChange() {
    currentIndex.value = swiperRef.value.$el.swiper.activeIndex;
  }
  function handlePrev() {
    if (currentIndex.value === 0) {
      return;
    }
    swiperRef.value?.$el.swiper.slidePrev();
  }
  function handleNext() {
    if (currentIndex.value === tabs.value.length - 1) {
      return;
    }
    console.log('handleNext');
    swiperRef.value?.$el.swiper.slideNext();
  }

  const tabs = ref<Recordable[]>([]);
  const defaultTabList = ref<Recordable[]>([]);
  const pipelineDict = ref<Recordable[]>([]);
  async function getTabsData() {
    const params = {
      bindSourceFrom: bindSourceFrom.value,
      bindSourceUniqueIds: [bindSourceUniqueId],
    };
    pipelineDict.value = await getDictTypeListApi({ type: DICT.PIPELINE_TYPE });
    const res = await getFlowDataListApi(params);
    defaultTabList.value = (res || []).filter((i) => i.flowName && i.isShow);
    checkedMenuId.value = defaultTabList.value[0]?.id;

    const tabData: any[] = [];

    // 获取 pipeline 对象
    const getPipelineObj = (pipelineType: number) =>
      pipelineDict.value.find((item) => Number(item.value) === pipelineType);

    // 处理单个 tabItem 的逻辑
    const processTabItem = (tabItem: any) => {
      const { pipelineType } = tabItem;

      // 如果没有 pipelineType，直接添加到 tabData
      if (!pipelineType) {
        tabData.push(tabItem);
        return;
      }

      // 检查 tabData 中是否已经存在相同的 pipelineType
      const existingTab = tabData.find((item) => Number(item.pipelineType) === pipelineType);

      if (existingTab) {
        // 如果存在，将当前 tabItem 添加到 children 中
        existingTab.children.push(tabItem);
      } else {
        // 如果不存在，创建新的 pipeline 对象并添加到 tabData
        const pipelineObj = getPipelineObj(pipelineType);
        tabData.push({
          pipelineName: pipelineObj?.label,
          pipelineType: pipelineObj?.value,
          children: [tabItem],
        });
      }
    };

    // 遍历 defaultTabList 并处理每个 tabItem
    defaultTabList.value.forEach(processTabItem);
    tabs.value = tabData;
  }
  getTabsData();

  const showNavigation = ref(false);

  function updateNavigationState() {
    function getSlidesWidth() {
      const slides: HTMLElement[] = swiperRef.value.$el.querySelectorAll('.swiper-slide');
      console.log('slides', slides);
      return Array.from(slides).reduce((acc, slide) => acc + slide.offsetWidth, 0);
    }
    // 获取所有slides的总宽度
    const slidesWidth = getSlidesWidth();
    console.log('slidesWidth', slidesWidth);
    // 获取容器宽度
    const containerWidth = swiperRef.value.$el.clientWidth;
    console.log('containerWidth', containerWidth);

    // 如果slides总宽度大于容器宽度，则需要显示导航
    showNavigation.value = slidesWidth > containerWidth;
  }

  watch(
    () => tabs.value,
    () => {
      nextTick(() => {
        if (swiperRef.value?.$el?.swiper) {
          updateNavigationState();
        }
      });
    },
    {
      immediate: true,
    },
  );

  const checkedMenuId = ref(0);
  async function switchTab(menu) {
    if (menu.id === checkedMenuId.value) return;
    tabs.value.filter((item) => item.pipelineType).forEach((item) => (item.dropdownActive = false));
    checkedMenuId.value = menu.id;
  }

  const flowId = computed(() => {
    const item = defaultTabList.value.find((i) => i.id === checkedMenuId.value);
    return item?.id || '';
  });

  function handleNavigationEvent(pen) {
    const findItem = defaultTabList.value.find((tab) => tab.id === pen.showDetail);
    if (!findItem) {
      return;
    }

    switchTab(findItem);
  }

  const swiperDisableBegin = computed(() => {
    return swiperRef.value?.$el.swiper?.isBeginning;
  });

  const swiperDisableEnd = computed(() => {
    return swiperRef.value?.$el.swiper?.isEnd;
  });

  const { currentRoute } = useRouter();
  const getStyle = computed(() => {
    return {
      backgroundColor: unref(currentRoute).meta?.bgColor || '#041946',
    };
  });
</script>

<style lang="less" scoped>
  // 提示: 无界模式下，样式文件放在script内，会影响设备流水线的table高度
  @import 'swiper/css/navigation';
  @import 'swiper/css';

  .control-main {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    // overflow: hidden;
    padding-top: 4px;

    .tab-box {
      padding: 0 1rem;
      // height: 49px;
      // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      & > .ant-row {
        border-bottom: 1px solid rgba(255, 255, 255, 0.24);
        padding-bottom: 16px;
      }

      .flow-swiper-container {
        position: relative;
        display: flex;
        gap: 8px;
        align-items: center;
        height: 100%;

        .app-iconify {
          cursor: pointer;
        }

        ::v-deep(.swiper) {
          margin-left: 0;
          margin-right: 0;

          .swiper-wrapper {
            display: flex;
          }

          .swiper-slide {
            width: auto;
          }
        }

        .tab {
          padding: 0 16px;
          height: 32px;
          cursor: pointer;

          .name {
            font-size: 14px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #b8b8b8;
            line-height: 32px;
            text-align: center;
          }

          &:hover .name {
            color: #fff;
          }

          &_actived {
            background: url('./assets/images/tab-borer.png') 0 0 no-repeat;
            background-size: 100% 100%;

            .name {
              font-weight: 600;
              background-image: linear-gradient(180deg, #ffffff 0%, #b0d0ff 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }

        .tab-actived {
          background: linear-gradient(180deg, rgba(3, 40, 85, 0.48) 0%, rgba(1, 32, 70, 0.24) 100%);
          box-shadow: inset 0px 0px 24px 0px #0661d1;
          border-radius: 4px 4px 4px 4px;
          position: relative;
          border-top: 1px solid rgba(255, 255, 255, 0.2);
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);

          ::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            border: 1px solid;
            border-image: linear-gradient(
                90deg,
                rgba(72.21238911151886, 139.23451513051987, 255, 0),
                rgba(72.21238911151886, 224.53539311885834, 255, 1),
                rgba(72.21238911151886, 139.23451513051987, 255, 0)
              )
              1 1;
          }

          .name {
            color: #fff;
            line-height: 30px;
          }
        }

        .custom-select-container {
          height: 32px;
          color: #b8b8b8;
          padding: 8px 16px;
          line-height: 1;
          cursor: pointer;

          &:hover {
            color: #fff;
          }
        }

        .dropdown-active {
          color: #fff;
          background: linear-gradient(180deg, rgba(3, 40, 85, 0.48) 0%, rgba(1, 32, 70, 0.24) 100%);
          box-shadow: inset 0px 0px 24px 0px #0661d1;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          padding: 7px 15px;
          position: relative;

          ::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            border: 1px solid;
            border-image: linear-gradient(
                90deg,
                rgba(72.21238911151886, 139.23451513051987, 255, 0),
                rgba(72.21238911151886, 224.53539311885834, 255, 1),
                rgba(72.21238911151886, 139.23451513051987, 255, 0)
              )
              1 1;
          }
        }
      }

      .header-right {
        height: 100%;
        display: flex;
        gap: 16px;
        justify-content: flex-end;
        align-items: center;

        .icon {
          width: 16px;
          cursor: pointer;
        }
      }
    }

    .spin-wrapper {
      position: absolute;
      left: 0;
      top: 49px;
      right: 0;
      bottom: 0;
      z-index: 100;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    #diagram-wrap {
      height: 100%;
      width: 100%;
      background: #041946 url('../../assets/images/bg.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
  }
</style>

<style lang="less">
  .options-box {
    .ant-dropdown-menu-item {
      &:hover {
        background-color: rgba(255, 255, 255, 0.16) !important;
      }
    }

    .ant-dropdown-menu {
      background: #071732;
      box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.56);
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(255, 255, 255, 0.32);
      padding: 4px !important;

      .ant-dropdown-menu-item {
        height: 32px;
        color: #ebebeb;
        border-radius: 4px !important;
      }

      .menu_actived {
        background-color: rgba(11, 98, 203, 0.88) !important;
        color: white;
      }
    }
  }
</style>
