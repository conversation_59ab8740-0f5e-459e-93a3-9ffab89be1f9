import { useRoute, type LocationQueryValue } from 'vue-router';
import { getAppEnvConfig } from '/@/utils/env';
import {
  EXPRESSION_TYPE_ENUM,
  INDICATOR_TYPE_ENUM,
  INDICATOR_NORMAL_VALUE,
} from '/@process-editor/constant';
import { CENTER_VIEW } from '/@process-editor/constant/process';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { FACTORY_KEY } from '/@/enums/cacheEnum';
import { createLocalStorage } from '/@/utils/cache';
import { cloneDeep } from 'lodash-es';
import { saveFlowDataDetailApi } from '/@process-editor/api/index';
import { useProcess } from '/@process-editor/hooks/useProcess';

const ls = createLocalStorage();
const userStore = useUserStoreWithOut();
const { VITE_GLOB_EDITOR_API_URL } = getAppEnvConfig();
const { getEditorLocalStorage, updateFlowVersion, updateCanvasData } = useProcess();

/**
 * 获取路由query参数
 * @returns
 */
export function getQueryParams() {
  const route = useRoute();
  const queryParams = {
    bindSourceFrom: 'Hlxb.Spocs',
    bindSourceUniqueId: '2001',
  };

  const query = route.query as Record<string, LocationQueryValue>;

  if (query) {
    queryParams.bindSourceFrom = query.bindSourceFrom || '';
    queryParams.bindSourceUniqueId = query.bindSourceUniqueId || '';
  }

  return queryParams;
}

/**
 * 获取设备工艺、在线中控室文件前缀地址
 * @returns
 */
export function getEditorPrefixFileUrl() {
  return VITE_GLOB_EDITOR_API_URL + '/industryflowapi/File/GetFile/';
}

/**
 * 获取设备工艺上传文件地址
 * @returns
 */
export function getUploadFileUrl() {
  return VITE_GLOB_EDITOR_API_URL + '/data-sence/resourceIndex/uploadFile';
}

export function isValidPath(path) {
  const pattern = /^(\/[^\/\0]+)+\/?$/;
  return pattern.test(path);
}

/**
 * 开发环境下添加资源路径前缀, 生产环境下不添加
 * @param {string} url 资源路径
 * @returns
 */
export function addResourcePrefix(url: string) {
  const Env = import.meta.env;
  const origin = Env.MODE === 'development' ? Env.VITE_DEV_DOMAIN : window.location.origin;

  return isValidPath(url) ? origin + url : url;
}

const deleteKeys = ['image', 'ResourcePicUrl', 'video'];

/**
 * 开发环境下删除资源前缀，生产环境下不删除
 * @param {Array} data 资源
 * @param {Array} keys 删除字段
 * @returns
 */
export function deleteResourcePrefix(data: Recordable[], keys = deleteKeys) {
  if (!Array.isArray(data)) return data;
  const Env = import.meta.env;
  const prefix = Env.MODE === 'development' ? Env.VITE_DEV_DOMAIN : window.location.origin;
  data.forEach((item) => {
    keys.forEach((key) => {
      if (item[key]) {
        item[key] = item[key].replace(prefix, '');
      }
    });
  });
  return data;
}

export function getFactoryIdFromUrl() {
  const params = location.href.slice(location.href.indexOf('?') + 1);
  const searchParams = new URLSearchParams(params);
  const factoryId = searchParams.get('factoryId');
  if (!factoryId) {
    console.error('未从url中获取到factoryId');
  }
  return factoryId;
}

export function getScriptTemplateByIndicatorItem(record) {
  let scriptTemplate = '';
  const {
    code = 'CODE',
    warningRuleId = '报警规则ID',
    indicatorType,
    expressionType,
  } = record || {};
  if (!indicatorType) return '';

  if (indicatorType === INDICATOR_TYPE_ENUM.TEXT) {
    // 文本
    const template = `
    /**
      * color 文本颜色 | text 文本值 | background 背景颜色 | width 宽 | height 高 | borderRadius: 边框圆角
      *  示例'{"color":"white","text":"文本33", "background": "blue", "width": 60, "height": 22, "borderRadius": 0.2}'
      */
    if (getV('${code}') > 100) {
      return '{"color":"red","text":"文本11"}';
    } else if (getV('${code}') > 50) {
      return '{"color":"yellow","text":"文本22"}';
    } else {
      return '{"color":"normal","text":"文本33"}';
    }
  `;
    scriptTemplate = template;
  } else if (indicatorType === INDICATOR_TYPE_ENUM.SWITCH) {
    // 开关
    const template = `
    /**
      * checked 快关状态 | changeVal 下控值 | onColor 开启颜色 | offColor 关闭颜色 
      *  示例'{"checked":0,"changeVal":1, "onColor":"#01a132","offColor":"#FFFFFF47"}'
      */
    if (getV('${code}') == 0) {
      return '{"checked":0,"changeVal":1}'; 
    } else if (getV('${code}') == 1) {
      return '{"checked":1,"changeVal":0}';
    } else {
      return '{"checked":0,"changeVal":1}';
    }
  `;
    scriptTemplate = template;
  } else if (indicatorType === INDICATOR_TYPE_ENUM.NUMBER) {
    // 数值 常规
    const template = `
    if (getV('${code}') > 100) {
      return 'red';
    } else if (getV('${code}') > 50) {
      return 'yellow';
    } else {
      return '';
    }
  `;
    scriptTemplate = template;
  } else if (expressionType === EXPRESSION_TYPE_ENUM.RULE) {
    // 数值 报警规则
    const template = `
    if (getV('${warningRuleId}') == 1) {
      return 'red';
    } else if (getV('${warningRuleId}') == 2) {
      return 'orange';
    } else if (getV('${warningRuleId}') == 3) {
      return 'yellow';
    } else {
      return 'normal';
    }
  `;
    scriptTemplate = template;
  } else if (!expressionType) {
    scriptTemplate = '';
  }
  return scriptTemplate;
}

/**
 * 获取指标值颜色
 * @param {Object} record 指标图元
 * @returns color 颜色
 */
export function getColorByIndicatorItem(record) {
  function getColorNotExpression() {
    const value = Number(record.expression);
    let color = record.normalColor || '#fff';

    if (record.upperLimits === '' && record.lowerLimits === '') {
      return color;
    }

    const upperLimits = Number(record.upperLimits);
    if (!Number.isNaN(upperLimits)) {
      const upperExpression = record.isUpperEqual ? value >= upperLimits : value > upperLimits;
      if (upperExpression) {
        return record.greaterThanUpperColor;
      }
    }

    const lowerLimits = Number(record.lowerLimits);
    if (!Number.isNaN(lowerLimits)) {
      const lowerExpression = record.isLowerEqual ? value <= lowerLimits : value < lowerLimits;
      if (lowerExpression) {
        return (color = record.lowerThanLowerColor);
      }
    }
    return color;
  }

  function getColorByIndicator() {
    return record.scriptResult && record.scriptResult !== INDICATOR_NORMAL_VALUE
      ? record.scriptResult
      : record.normalColor;
  }

  function getColorByWarningRule() {
    return record.scriptResult && record.scriptResult !== INDICATOR_NORMAL_VALUE
      ? record.scriptResult
      : record.normalColor;
  }

  let color = '';
  switch (record.scriptType) {
    case EXPRESSION_TYPE_ENUM.INDICATOR:
      color = getColorByIndicator();
      break;
    case EXPRESSION_TYPE_ENUM.RULE:
      color = getColorByWarningRule();
      break;
    default:
      color = getColorNotExpression();
      break;
  }

  return color;
}

export function parseJson(str, target = 'object', defaultValue = {}) {
  const config = {
    object: { defaultVal: {}, checkFn: (res) => typeof res === 'object' },
    array: { defaultVal: [], checkFn: (res) => Array.isArray(res) },
    string: { defaultVal: '', checkFn: (res) => typeof res === 'string' },
    number: { defaultVal: 0, checkFn: (res) => typeof res === 'number' },
    boolean: { defaultVal: false, checkFn: (res) => typeof res === 'boolean' },
  };
  let { defaultVal } = config[target];
  const { checkFn } = config[target];
  defaultVal = defaultValue !== undefined ? defaultValue : defaultVal;
  let res = defaultVal;
  try {
    res = JSON.parse(str);
  } catch (error) {
    return defaultVal;
  }
  if (!checkFn(res)) return defaultVal;
  return res;
}

export function getFactoryId() {
  let isEditor = location.href.includes('process-editor');
  // 编辑页面从url中获取factoryId
  let factoryId = isEditor ? parseQueryParams(location.href)?.factoryId : '';
  factoryId = factoryId || ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId;
  return factoryId;
}

function parseQueryParams(url) {
  const params = {};
  // 查找问号的位置
  const queryStart = url.indexOf('?');

  // 如果没有问号，直接返回空对象
  if (queryStart === -1) {
    return params;
  }
  // 获取问号后面的所有内容
  const queryString = url.slice(queryStart + 1);
  // 按&符号分割参数
  const paramPairs = queryString.split('&');
  // 遍历每个参数对
  paramPairs.forEach((pair) => {
    // 按=符号分割键和值
    const [key, value] = pair.split('=');
    // 解码值（处理中文和特殊字符）并添加到对象
    if (key) {
      params[key] = value ? decodeURIComponent(value) : '';
    }
  });
  console.log('-----------------> params', params);
  return params;
}

export function getFlowDataByMeta2dData() {
  if (!meta2d) return null;
  const data = meta2d.data();
  data.pens = data.pens.filter((i) => !i?.category);
  if (!data.bkImage) delete data.bkImage;
  if (!data.ruleColor) delete data.ruleColor;
  delete data.originPens;
  return cloneDeep(data);
}
export function getFlowDataStrByMeta2dData() {
  const res = getFlowDataByMeta2dData();
  if (!res) return '';
  return JSON.stringify(res);
}

export async function saveFlowDataDetail(params, options = {}) {
  const { paramsProcessFn, resProcessFn } = options;
  if (!meta2d) return;
  const jsonData = meta2d.data();
  // 删除无用数据
  jsonData.pens = jsonData.pens.filter((i) => i?.category !== 'kit');
  jsonData.pens = jsonData.pens.filter((i) => !i.category);

  jsonData.pens = deleteResourcePrefix(jsonData.pens);
  jsonData.centerView = getEditorLocalStorage(CENTER_VIEW) || false;
  if (jsonData.bkImage) delete jsonData.bkImage;
  if (jsonData.originPens) delete jsonData.originPens;
  params.dataJson = jsonData;
  if (paramsProcessFn) paramsProcessFn(params);
  params.dataJson = JSON.stringify(params.dataJson || '{}');

  const flowParams = params;
  let flowData;
  try {
    flowData = await saveFlowDataDetailApi(flowParams);
    flowData.dataJson = JSON.parse(flowData.dataJson || '{}');
    //  更新数据
    flowData.dataJson.originPens = cloneDeep(flowData.dataJson?.pens);
    if (resProcessFn) resProcessFn(flowData);
    updateFlowVersion(flowData.curVersion);
    updateCanvasData(flowData.dataJson);
    return flowData;
  } catch (error) {
    console.error('保存流程图失败', error);
    return null;
  }
}
