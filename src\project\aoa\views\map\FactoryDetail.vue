<template>
  <BasicDrawer
    title="水厂详情"
    :width="360"
    :footerHeight="72"
    rootClassName="factory-detail-drawer"
    showFooter
    v-bind="$attrs"
    :getContainer="false"
    @register="registerDrawer"
  >
    <template #footer>
      <div class="detail-close">
        <Button @click="handleClose">关闭</Button>
      </div>
    </template>
    <div class="detail-body">
      <div class="detail-content">
        <div class="detail-swiper" v-if="info?.imgs.length">
          <Swiper
            :options="options"
            :modules="modules"
            :pagination="{ clickable: true }"
            navigation
            @slideChange="onSlideChange"
          >
            <SwiperSlide v-for="(item, index) in info?.imgs" :key="index">
              <img :src="getEevReturnDomain(item)" />
            </SwiperSlide>
            <div class="swiper-pagination-page">{{ currentImgIndex }}/{{ info?.imgs.length }}</div>
          </Swiper>
        </div>
        <div class="detail-swiper-empty" v-else></div>
        <div class="info">
          <div class="item">
            <div class="item-title">水厂名称</div>
            <div class="item-content">{{ info?.name }}</div>
          </div>
          <div class="item">
            <div class="item-title">日处理量</div>
            <div class="item-content"
              >{{ info?.dayProcessingCapacity }}
              {{ info?.dayProcessingCapacity !== null ? 't' : '' }}</div
            >
          </div>
          <div class="item">
            <div class="item-title">日进水量</div>
            <div class="item-content"
              >{{ info?.dayWaterIntake }} {{ info?.dayWaterIntake !== null ? 't' : '' }}</div
            >
          </div>
          <div class="item">
            <div class="item-title">总风量</div>
            <div class="item-content"
              >{{ info?.totalAirVolume }} {{ info?.totalAirVolume !== null ? 'm³/h' : '' }}</div
            >
          </div>
          <div class="item">
            <div class="item-title">日耗电量</div>
            <div class="item-content"
              >{{ info?.dayPowerConsumption }}
              {{ info?.dayPowerConsumption !== null ? 'kWh' : '' }}</div
            >
          </div>
          <div class="item">
            <div class="item-title">总览地址</div>
            <div class="item-content item-content-href" v-if="info?.hrefUrl">
              <Tooltip :title="info?.hrefUrl" placement="leftTop">
                <a :href="info?.hrefUrl" target="_blank">{{ info?.hrefUrl }}</a>
              </Tooltip>
            </div>
            <div class="item-content empty" v-else>该水厂暂无总览地址</div>
          </div>
          <div class="item introduction">
            <div class="item-title">水厂简介</div>
            <div class="item-content" v-if="info?.introduction">{{ info?.introduction }}</div>
            <div class="item-content empty" v-else>该水厂暂无简介</div>
          </div>
          <div class="item introduction">
            <div class="item-title">水厂详细介绍</div>
            <div
              class="item-content"
              v-html="info?.detailIntroduction"
              v-if="info?.detailIntroduction"
            ></div>
            <div class="item-content empty" v-else>该水厂暂无详细介绍</div>
          </div>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { Button, Tooltip } from 'ant-design-vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { getEevReturnDomain } from '/@/utils/file/url';
  import { Pagination, Navigation } from 'swiper/modules';
  import 'swiper/css';
  import 'swiper/css/pagination';
  import 'swiper/css/navigation';

  defineEmits(['register']);

  const options = ref({
    slidesPerView: 1,
    spaceBetween: 0,
  });

  const modules = ref([Pagination, Navigation]);

  const info = ref();
  const currentImgIndex = ref(1);

  const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
    info.value = data.data;
  });

  const handleClose = () => {
    closeDrawer();
  };

  const onSlideChange = (swiper) => {
    currentImgIndex.value = swiper.activeIndex + 1;
  };
</script>

<style lang="less">
  .factory-detail-drawer {
    .ant-drawer-mask {
      display: none;
    }

    .ant-drawer-content-wrapper {
      box-shadow: -4px 4px 12px 0px rgba(0, 0, 0, 0.12);

      .ant-drawer-content {
        .ant-drawer-header {
          height: 64px;
          padding: 0 20px;

          .ant-drawer-header-title {
            position: relative;

            .ant-drawer-close {
              position: absolute;
              right: 0;
              z-index: 1;
            }

            .ant-drawer-title {
              font-weight: 600;
              font-size: 16px;
              color: #333333;
              line-height: 16px;
            }
          }
        }

        .ant-drawer-body {
          padding: 0;

          .scrollbar__wrap {
            padding: 0 20px;
            height: calc(100% - 12px);
            margin-top: 12px;

            .scrollbar__bar {
              right: 0;
            }
          }

          .vben-basic-drawer-footer {
            padding: 0 20px;
          }

          .detail-body {
            .detail-content {
              font-family: 'PingFang SC, PingFang SC';

              .detail-swiper-empty {
                width: 100%;
                height: 160px;
                border-radius: 4px 4px 4px 4px;
                margin-bottom: 24px;
                overflow: hidden;
                background: #f0f0f0 url('./assets/images/empty.png') center center no-repeat;
                background-size: 24px 24px;
              }

              .detail-swiper {
                margin-bottom: 24px;
                width: 100%;
                height: 160px;
                border-radius: 4px;
                overflow: hidden;

                .swiper {
                  height: 100%;

                  &:hover {
                    .swiper-button-prev,
                    .swiper-button-next {
                      opacity: 1;
                      z-index: 1;
                    }
                  }

                  .swiper-button-prev,
                  .swiper-button-next {
                    opacity: 0;
                    width: 22px;
                    height: 32px;
                    margin-top: -16px;
                    background: rgba(255, 255, 255, 0.56);
                    z-index: -1;

                    &:hover {
                      // background: var(--theme-color);
                      background: rgb(17, 144, 120);

                      &:after {
                        color: #fff;
                      }
                    }

                    &:after {
                      font-size: 14px;
                      font-weight: 600;
                      color: #333;
                    }
                  }

                  .swiper-button-prev {
                    border-radius: 0px 2px 2px 0px;
                    left: 0;
                  }

                  .swiper-button-next {
                    border-radius: 2px 0 0 2px;
                    right: 0;
                  }

                  .swiper-pagination {
                    bottom: 4px;

                    .swiper-pagination-bullet {
                      width: 12px;
                      height: 6px;
                      // background: #f0f0f0;
                      background: #d0d3d6;

                      border-radius: 3px;
                      margin: 0 4px;
                      opacity: 1;

                      &.swiper-pagination-bullet-active {
                        width: 24px;
                        // background: var(--theme-color);
                        background: #119078;
                      }
                    }
                  }

                  .swiper-pagination-page {
                    position: absolute;
                    right: 0;
                    top: 0;
                    padding: 0 5px;
                    height: 24px;
                    background: #d0d3d6;
                    border-radius: 0px 4px 0px 4px;
                    z-index: 10;
                    line-height: 24px;
                    text-align: center;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                  }
                }
              }

              .info {
                display: flex;
                justify-content: flex-start;
                flex-wrap: wrap;
                gap: 0 16px;

                .item {
                  margin-bottom: 24px;
                  width: calc(50% - 8px);

                  .item-title {
                    font-weight: 400;
                    font-size: 14px;
                    color: #999999;
                    line-height: 14px;
                  }

                  .item-content {
                    padding-top: 12px;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    line-height: 21px;

                    &.empty {
                      color: #666;
                    }

                    &.item-content-href {
                      color: var(--theme-color);
                      text-decoration: underline;
                      text-overflow: ellipsis;
                      overflow: hidden;
                      white-space: nowrap;

                      a {
                        color: var(--theme-color);
                      }
                    }

                    &.item-content-run-1 {
                      color: #4db803;
                    }

                    &.item-content-run-2 {
                      color: var(--theme-color);
                    }

                    &.item-content-run-3 {
                      color: #ff522b;
                    }

                    &.item-content-run-4 {
                      color: #ff0000;
                    }

                    &.item-content-run-5 {
                      color: #2238fc;
                    }

                    &.item-content-run-6 {
                      color: #fc7c22;
                    }
                  }

                  &.introduction {
                    width: 100%;
                  }
                }
              }
            }

            .detail-close {
              padding: 0 20px;
              height: 72px;
              display: flex;
              align-items: center;
              justify-content: flex-end;
            }
          }
        }
      }
    }
  }
</style>
