import { useDomainStoreWithOut } from '/@/store/modules/domain';
import { getPublicinfoApi } from '/@/api/sys/user';
import { computed } from 'vue';
import { pick } from 'lodash-es';
import { getEevReturnDomain } from '/@/utils/url';
import { useAppStore } from '/@/store/modules/app';
import { themeSetting } from '/@/settings/projectSetting';
import { cloneDeep, merge } from 'lodash-es';
import { parseJson } from '/@/utils';
export function useDomain() {
  const domainStore = useDomainStoreWithOut();
  const getTenantId = computed(() => domainStore.getTenantId);
  const getDomain = computed(() => domainStore.getDomain);
  const getRememberMe = computed(() => domainStore.getrRememberMe);
  const getUseConfig = computed(() => domainStore.getUseConfig);
  const getUseProjectSetting = computed(() => domainStore.getProjectInfo);
  const getProjectCode = computed(() => domainStore.getProjectCode);
  const showVerify = computed(() => domainStore.getVerify);

  function changeDomain(domain) {
    domainStore.setDomainInfo(domain);
  }
  async function changeTenantId(id) {
    await domainStore.setTenantId(id);
    updateProjectSetting();
  }

  function updateRememberMe(remember) {
    domainStore.setRememberMe(remember);
  }
  function updateUserConfig(user) {
    domainStore.setUser(user);
  }
  function setProjectInfo(info) {
    domainStore.setProjectInfo(info);
  }
  function changeProjectInfo(projectSetting) {
    domainStore.setProjectInfo(projectSetting);
  }

  function changeProjectCode(projectCode) {
    domainStore.setProjectCode(projectCode);
  }

  const updateProjectSetting = async () => {
    if (domainStore.getTenantId) {
      const res = await getPublicinfoApi(domainStore.getTenantId);

      const projectSetting = {
        icon: getEevReturnDomain(res.icon?.url),
        logo: getEevReturnDomain(res.logo?.url),
        logo2: getEevReturnDomain(res.logo2?.url),
        loginBgImg: getEevReturnDomain(res.loginBgImg?.url),
        loginFrameBgImg: getEevReturnDomain(res.loginFrameBgImg?.url),
        appQrCodeImg: getEevReturnDomain(res.appQrCodeImg?.url),
        ...pick(res, ['title', 'subTitle', 'loginFrameBgColor']),
      };
      const appStore = useAppStore();
      const themeConfig = parseJson(res.themeConfig);

      appStore.setThemeConfig(
        merge(
          cloneDeep(themeSetting),
          res.themeConfig ? themeConfig : undefined,
          appStore.getThemeConfig ? appStore.getThemeConfig : undefined,
        ),
      );
      setTimeout(() => {
        setProjectInfo(projectSetting);
      }, 60);
      return res;
    }
    return {};
  };
  return {
    getTenantId,
    getDomain,
    getRememberMe,
    getUseConfig,
    getUseProjectSetting,
    getProjectCode,
    showVerify,
    changeDomain,
    changeTenantId,
    changeProjectCode,
    updateRememberMe,
    updateUserConfig,
    setProjectInfo,
    changeProjectInfo,
    updateProjectSetting,
  };
}
