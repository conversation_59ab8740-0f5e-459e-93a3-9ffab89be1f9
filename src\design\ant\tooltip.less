.ant-tooltip {
  .width-prop(420,max-width) !important;
  .font-size(14);

  .ant-tooltip-arrow {
    &::before {
      background: #025849;
      box-shadow: 0 0 1px 0 #fff;
    }

    &::after {
      border: 1px solid #9ec5bf;
      box-shadow: 0 0 3px 0 #1d2e1e;
    }
  }

  &-placement-topLeft,
  &-placement-topRight,
  &-placement-top {
    .ant-tooltip-arrow {
      bottom: 1px;
    }
  }

  &-placement-bottomLeft,
  &-placement-bottomRight,
  &-placement-bottom {
    .ant-tooltip-arrow {
      top: 1px;
    }
  }

  &-placement-rightTop,
  &-placement-right,
  &-placement-rightBottom {
    .ant-tooltip-arrow {
      left: 1px;
    }
  }

  &-placement-leftTop,
  &-placement-left,
  &-placement-leftBottom {
    .ant-tooltip-arrow {
      right: 1px;
    }
  }

  .ant-tooltip-inner {
    background: linear-gradient(to bottom, #015848, #025849);
    box-shadow: 0 4px 6px 0 #1d2e1e;
    border: 1px solid #9ec5bf;
  }
}
