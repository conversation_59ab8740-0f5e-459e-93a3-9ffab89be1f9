#app {
  width: 100%;
  height: 100%;
}

// =================================
// ==============scrollbar==========
// =================================
body > * {
  ::-webkit-scrollbar {
    width: 8px;
    height: 9px;
    background: @scrollbar !important;
    border-radius: 8px !important;
  }

  ::-webkit-scrollbar-button {
    display: none !important;
  }

  ::-webkit-scrollbar-track {
    // background: rgb(0 0 0 / 8%);
    background: @scrollbar !important;
    border-radius: 8px;
  }

  ::-webkit-scrollbar-corner {
    background: @scrollbar !important;
  }

  ::-webkit-scrollbar-thumb {
    // background: rgb(0 0 0 / 16%);
    background: @scrollbar-thumb !important;
    border-radius: 8px !important;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: @scrollbar-thumb-hover !important;
  }
}

// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: @primary-color;
    opacity: 0.75;
  }
}

img,
svg {
  display: inline-block;
}
