<template>
  <a-form
    :label-col="{ span: 8 }"
    :wrapper-col="{ span: 16 }"
    :colon="false"
    labelAlign="left"
    autocomplete="off"
  >
    <template v-for="(formItem, formIndex) in formList" :key="formIndex">
      <a-form-item label="是否显示">
        <a-radio-group
          v-model:value="formItem.data.displayMode"
          :options="displayOptions"
          @change="onDisplayChange"
        />
      </a-form-item>
      <a-form-item label="数据源">
        <a-select
          v-model:value="indexIds"
          :mode="data.businessData.itemType === 'base' ? 'multiple' : 'default'"
          style="width: 100%"
          placeholder="请选择指标"
          :show-search="true"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          :options="indexOptions"
          @search="handleSearch"
          @change="onIndexChange"
        />
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 24 }" v-show="tags.length" class="tag-item">
        <a-space direction="vertical" style="width: 100%; overflow: hidden">
          <div
            v-for="(item, tagIndex) in tags"
            :key="tagIndex"
            class="item"
            style="width: 100%; display: flex; gap: 16px"
          >
            <a-tag style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ item.label }}
            </a-tag>
            <span class="text" @click="copy(item.value)">复 制</span>
          </div>
        </a-space>
      </a-form-item>
      <a-form-item label="小元件类型">
        <a-select v-model:value="formItem.data.businessData.itemType" @change="onItemTypeChange">
          <a-select-option
            v-for="(item, typeIndex) in itemTypeOptions"
            :key="typeIndex"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="关联管线">
        <a-select
          v-model:value="formItem.data.businessData.pipelineIds"
          placeholder="管线"
          mode="tags"
        >
          <a-select-option v-for="i in pipelineOptions" :key="i.label" :value="i.value">
            {{ i.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="图片源" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
        <a-upload
          v-model:file-list="fileList"
          :action="uploadUrl"
          list-type="picture-card"
          class="avatar-uploader"
          :before-upload="beforeUpload"
          :headers="{
            Authorization: `${getToken()}`,
            'X-De-Token': `${getToken()}`,
            'Tenant-Id': `${tenantId}`,
          }"
          @preview="handlePreview"
        >
          <div v-if="fileList.length < 8">
            <plus-outlined />
            <div style="margin-top: 8px">上传</div>
          </div>
          <a-modal
            :open="previewVisible"
            :title="previewTitle"
            :footer="null"
            @cancel="handlePreviewCancel"
          >
            <img alt="example" style="width: 100%" :src="previewImage" />
          </a-modal>
        </a-upload>
      </a-form-item>
      <a-form-item label="计算表达式">
        <a-button style="width: 100%" @click="openEditorModal"> ... </a-button>
      </a-form-item>
    </template>
  </a-form>
  <template v-for="(formItem, formIndex) in formList" :key="formIndex">
    <BasicModal
      v-model:open="visible"
      title="Javascript"
      :width="800"
      wrapClassName="editor-modal"
      :bodyStyle="{ padding: 0 }"
    >
      <div class="text-right pb-3">
        <a-button type="second" @click="createTemplate"> 生成模版 </a-button>
      </div>
      <div style="height: 400px">
        <json-editor v-if="visible" v-model="formItem.data.businessData.script" ref="editorRef" />
      </div>
      <template #footer>
        <a-button @click="handleCancel"> 取消 </a-button>
        <a-button type="primary" @click="handleOk"> 确定 </a-button>
      </template>
    </BasicModal>
  </template>
</template>

<script>
  import { Form, FormItem, Tag, Select, Modal, RadioGroup, Space, Upload } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { displayOptions, itemTypeOptions } from './kit.data';
  import JsonEditor from '../JsonEditor.vue';
  import { BasicModal } from '/@/components/Modal';
  import { addResourcePrefix } from '/@process-editor/utils/index';

  const AForm = Form;
  const AFormItem = FormItem;
  const ASelect = Select;
  const ASelectOption = Select.Option;
  const ARadioGroup = RadioGroup;
  const ATag = Tag;
  const AModal = Modal;
  const ASpace = Space;
  const AUpload = Upload;

  export {};
</script>

<script setup>
  import { ref, watch, onMounted } from 'vue';
  import { useProcess } from '/@process-editor/hooks/useProcess';
  import { PLATFORM_INFO } from '/@process-editor/constant/process';
  import { getAllFlowTagsApi, Api } from '/@process-editor/api/index';
  import { getAppEnvConfig } from '/@/utils/env';
  import { updatePropData } from './utils';
  import { getToken } from '/@/utils/auth';
  import { createLocalStorage } from '/@/utils/cache';
  import { TENANTID_KEY } from '/@/enums/cacheEnum';

  const { VITE_GLOB_API_URL } = getAppEnvConfig();
  const ls = createLocalStorage();

  const tenantId = ls.get(TENANTID_KEY) || '';

  const props = defineProps({
    formList: {
      type: Array,
      default: () => [
        {
          data: {},
        },
      ],
    },
    data: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
    activePen: {
      type: Object,
      default: () => {},
    },
    update: {
      type: Boolean,
      default: false,
    },
  });
  const UPDATE_DATA = 'update:data';
  const emits = defineEmits(['change-view-mode', 'update:data']);

  const { getEditorLocalStorage } = useProcess();

  const uploadUrl = `${VITE_GLOB_API_URL}${Api.UPLOAD_FILE}`;

  const indexIds = ref([]);
  const indexOptions = ref([]);
  const tags = ref([]);

  const previewVisible = ref(false);
  const previewImage = ref('');
  const previewTitle = ref('');

  const fileList = ref([]);

  const { createMessage } = useMessage();
  const message = window.__POWERED_BY_WUJIE__ ? window.message : createMessage;

  const editorRef = ref(null);

  watch(
    () => props.update,
    async (newVal) => {
      if (newVal) {
        await getIndexData();
        function initFormData() {
          indexIds.value = props.data.businessData.productDataInfos.map((i) => i.dataUniqueId);

          tags.value = indexOptions.value.filter((i) => indexIds.value.includes(i.value));

          fileList.value = props.data.businessData.semaphoreImageList.map((i, index) => ({
            uid: index,
            name: i.imageName,
            status: 'done',
            url: addResourcePrefix(i.imagePath),
          }));

          updateFormDataByProp((data) => {
            data.businessData.itemWidth = props.activePen.width;
            data.businessData.itemHeight = props.activePen.height;
          });
        }
        initFormData();
      }
    },
    {
      immediate: true,
    },
  );

  watch(
    () => fileList.value,
    (newVal) => {
      updateFormDataByProp((data) => {
        data.businessData.semaphoreImageList = newVal
          .map((i) => {
            let url = i.url || i.response?.data;
            if (!url) return;
            return {
              imagePath: url,
              ImageName: '',
            };
          })
          .filter((i) => i);
      });
    },
  );

  const pipelineOptions = ref([]);
  function getPipelineList() {
    const data = window.meta2d.data();
    const pens = data.pens || [];
    pipelineOptions.value = pens
      .filter((i) => i.name === 'line')
      .map((i) => ({
        label: `管线-${i.id}`,
        value: i.id,
      }));
  }
  onMounted(() => {
    meta2d.on('editPen', () => {
      getPipelineList();
      const pens = meta2d.find(props.activePen.id);
      if (Array.isArray(pens)) {
        const pen = pens[0];
        updateFormDataByProp((data) => {
          data.positionX = pen.x;
          data.positionY = pen.y;
        });
      }
    });
  });

  function updateFormDataByProp(cb) {
    const params = [props.data, emits, UPDATE_DATA];
    updatePropData(params, cb);
  }

  function onItemTypeChange(e) {
    if (e === 'base') {
      indexIds.value = Array.isArray(indexIds.value) ? indexIds.value : [indexIds.value];
    } else if (e === 'blower') {
      indexIds.value = Array.isArray(indexIds.value) ? indexIds.value[0] : indexIds.value;
    }

    updateFormDataByProp((data) => {
      data.businessData.itemType = e;
    });
  }

  function onDisplayChange() {
    emits('change-view-mode');
  }

  async function getIndexData() {
    const platformInfo = getEditorLocalStorage(PLATFORM_INFO);
    const params = {
      searchText: '',
      bindSourceUniqueId: '',
    };
    const res = await getAllFlowTagsApi(params);
    indexOptions.value = res.map((r) => ({
      value: r.tagNameId,
      label: `${r.tagName}(${r.tagNameId})`,
      name: r.tagName,
      rootTagNameId: r.rootTagNameId,
    }));
  }

  let timeout = null;
  let currentValue = '';
  function fetch(value, callback) {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    currentValue = value;

    async function fake() {
      const platformInfo = getEditorLocalStorage(PLATFORM_INFO);
      const params = {
        searchText: value,
        bindSourceUniqueId: '',
      };
      const res = await getAllFlowTagsApi(params);
      if (currentValue === value) {
        const data = [];
        res.forEach((r) => {
          data.push({
            value: r.tagNameId,
            label: `${r.tagName}(${r.tagNameId})`,
            name: r.tagName,
            rootTagNameId: r.rootTagNameId,
          });
        });
        callback(data);
      }
    }

    timeout = setTimeout(fake, 300);
  }
  function handleSearch(val) {
    fetch(val, (d) => (indexOptions.value = d));
  }

  function onIndexChange(val) {
    updateFormDataByProp((data) => {
      const value = Array.isArray(val) ? val : [val];
      data.businessData.productDataInfos = value.map((id) => ({
        dataUniqueId: id,
        dataValue: '',
      }));
    });
    const multiple = indexOptions.value.filter((i) => indexIds.value.includes(i.value));
    const simple = indexOptions.value.find((i) => i.value === val);
    tags.value = props.data.businessData.itemType === 'base' ? multiple : [simple];
    fetch(val, (d) => (indexOptions.value = d));
  }

  function copy(value) {
    const input = document.createElement('input');
    input.setAttribute('readonly', 'readonly');
    input.setAttribute('value', value);
    document.body.appendChild(input);
    input.select();
    input.setSelectionRange(0, 9999);
    if (document.execCommand('copy')) {
      document.execCommand('copy');
      message.success('复制成功');
    }
    document.body.removeChild(input);
  }

  function getBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  }

  function handlePreviewCancel() {
    previewVisible.value = false;
    previewTitle.value = '';
  }

  async function handlePreview(file) {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    previewImage.value = file.url || file.preview;
    previewVisible.value = true;
    previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
  }

  function beforeUpload(file) {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传图片!');
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片必须小于5MB!');
    }
    return isJpgOrPng && isLt5M;
  }

  const visible = ref(false);
  function openEditorModal() {
    visible.value = true;
  }

  function handleOk() {
    visible.value = false;
  }

  function handleCancel() {
    visible.value = false;
  }

  function createTemplate() {
    const template = `
      if (getV('指标CODE') > 100) {
        return 0;
      } else if (getV('指标CODE') > 50) {
        return 1;
      } else {
        return null;
      }
    `;
    const editor = editorRef.value[0];
    editor.setValue(template);
    editor.formatDocument();
  }
</script>

<style lang="less">
  // .avatar-uploader .ant-upload {
  //   width: 80px;
  //   height: 80px;
  // }

  // .ant-upload-list-picture-card-container {
  //   width: 80px;
  //   height: 80px;
  // }

  // .ant-upload-select-picture-card i {
  //   font-size: 32px;
  //   color: #999;
  // }

  // .ant-upload-select-picture-card .ant-upload-text {
  //   margin-top: 8px;
  //   color: #666;
  // }

  // .tag-item {
  //   .text {
  //     font-size: 12px;
  //     flex-shrink: 0;
  //     line-height: 22px;
  //     cursor: pointer;
  //     color: @primary-color;
  //   }
  // }
</style>
