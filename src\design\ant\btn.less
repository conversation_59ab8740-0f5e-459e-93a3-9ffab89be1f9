/* stylelint-disable-next-line max-line-length */
// button reset
.ant-btn-group {
  .ant-btn {
    border-radius: 4px !important;
  }
}

.ant-btn {
  // padding: 0 16px;
  padding: 0 12px;
  box-shadow: none !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  .ant-wave {
    display: none;
  }

  & > .anticon + span {
    margin-inline-start: 4px;
  }

  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &-link {
    color: @aoa3-join-active-text;

    &:not([disabled]) {
      &:hover,
      &:active {
        color: @aoa3-join-active-text;
        border-color: transparent !important;
      }
    }
  }

  &-default:not(&-primary):not(&-second):not(&-error):not(&-dangerous) {
    color: @button-cancel-color;
    background-color: @button-cancel-bg-color;
    border-color: @button-cancel-border-color !important;

    // .app-iconify {
    //   color: #8f959e;
    // }

    &:not([disabled]) {
      &:hover {
        background-color: #f8f8f8 !important;
      }
    }
  }

  &-primary:not(&-dangerous) {
    border: 1px solid transparent !important;
    color: @white !important;
    background-color: @theme-color !important;

    &:not([disabled]) {
      &:hover {
        background-color: @theme-color-88p !important;
      }
    }
  }

  &-background-ghost:not(&-dangerous):not(&-default) {
    background-color: #fff !important;
    border: 1px solid var(--theme-color) !important;
    color: var(--theme-color) !important;

    &:not([disabled]) {
      &:hover {
        background-color: @theme-color-4p !important;
      }
    }
  }

  &-second {
    color: @aoa3-join-active-text !important;
    background-color: @theme-color-4p;
    border-color: @aoa3-join-active-text !important;

    &:not([disabled]) {
      &:hover,
      &:active {
        background-color: @theme-color-8p !important;
      }
    }
  }

  &-error&-background-ghost:not(&-link) {
    border-color: fade(@button-error-color, 30%);
    color: @button-error-color;

    &:not([disabled]) {
      &:hover,
      &:active {
        background-color: fade(@button-error-color, 5%) !important;
        border-color: @button-error-color;
        color: @button-error-color;
      }
    }
  }

  &-dangerous {
    background-color: #fff !important;
    border-color: fade(@danger-color, 56) !important;
    color: @danger-color !important;

    &:disabled {
      border-color: fade(@danger-color, 56) !important;
      color: @danger-color !important;
      opacity: 0.5 !important;
      background: #fff !important;
    }

    &:not(&:disabled) {
      &:hover,
      &:focus {
        background-color: #fdf2f0 !important;
        border-color: @danger-color !important;
        color: @danger-color !important;
      }
    }

    // &:hover,
    // &:focus {
    //   background: fade(@danger-color, 5%) !important;
    // }
  }

  &-dangerous:not(&-background-ghost) {
    &:disabled {
      background: @danger-color !important;
      color: #fff !important;
    }
  }

  [data-theme='light'] &.ant-btn-link.is-disabled {
    // color: rgb(0 0 0 / 25%);
    text-shadow: none;
    cursor: not-allowed !important;
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none;
    color: @theme-color;
    opacity: 0.5;
  }

  [data-theme='dark'] &.ant-btn-link.is-disabled {
    color: rgb(255 255 255 / 25%) !important;
    text-shadow: none;
    cursor: not-allowed !important;
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none;
  }

  // color: @white;

  &-success.ant-btn-link:not([disabled='disabled']) {
    color: @button-success-color;

    &:hover,
    &:focus {
      color: @button-success-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-success-active-color;
    }
  }

  &-success.ant-btn-link.ant-btn-loading,
  &-warning.ant-btn-link.ant-btn-loading,
  &-error.ant-btn-link.ant-btn-loading,
  &-background-ghost.ant-btn-link.ant-btn-loading,
  &.ant-btn-link.ant-btn-loading {
    &::before {
      background: transparent;
    }
  }

  &-success:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-success-color;
    border-color: @button-success-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-success-hover-color;
      border-color: @button-success-hover-color;
    }

    &:active {
      background-color: @button-success-active-color;
      border-color: @button-success-active-color;
    }
  }

  &-warning.ant-btn-link:not([disabled='disabled']) {
    color: @button-warn-color;

    &:hover,
    &:focus {
      color: @button-warn-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-warn-active-color;
    }
  }

  &-warning:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-warn-color;
    border-color: @button-warn-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-warn-hover-color;
      border-color: @button-warn-hover-color;
    }

    &:active {
      background-color: @button-warn-active-color;
      border-color: @button-warn-active-color;
    }

    //&[disabled],
    //&[disabled]:hover {
    //  color: @white;
    //  background-color: fade(@button-warn-color, 40%);
    //  border-color: fade(@button-warn-color, 40%);
    //}
  }

  &-error.ant-btn-link:not([disabled='disabled']) {
    color: @button-error-color;

    &:hover,
    &:focus {
      color: @button-error-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-error-active-color;
    }
  }

  &-dashed&-background-ghost,
  &-default&-background-ghost {
    color: @button-ghost-color;
    border-color: @button-ghost-color;

    &:hover,
    &:focus {
      color: @button-ghost-hover-color;
      border-color: @button-ghost-hover-color;
    }

    &:active {
      color: @button-ghost-active-color;
      border-color: @button-ghost-active-color;
    }

    &[disabled],
    &[disabled]:hover {
      color: fade(@white, 40%) !important;
      border-color: fade(@white, 40%) !important;
    }
  }

  &-background-ghost&-success:not(.ant-btn-link) {
    color: @button-success-color;
    background-color: #fff !important;
    border-color: @button-success-color;
    border-width: 1px;

    &:hover,
    &:focus {
      color: @button-success-hover-color !important;
      border-color: @button-success-hover-color;
    }

    &:active {
      color: @button-success-active-color;
      border-color: @button-success-active-color;
    }
  }

  &-background-ghost&-warning:not(.ant-btn-link) {
    color: @button-warn-color;
    background-color: #fff !important;
    border-color: @button-warn-color;
    border-width: 1px;

    &:hover,
    &:focus {
      color: @button-warn-hover-color !important;
      border-color: @button-warn-hover-color;
    }

    &:active {
      color: @button-warn-active-color;
      border-color: @button-warn-active-color;
    }
  }

  &-ghost.ant-btn-link:not([disabled='disabled']) {
    color: @button-ghost-color;

    &:hover,
    &:focus {
      color: @button-ghost-hover-color;
      border-color: transparent;
    }
  }
}
