<template>
  <div
    :class="['tipsITem-component', classSet ? 'tipsITem_pd' : 'tipsITem_pd2']"
    @click="handleClick"
    :theme="data.config.theme"
    :style="`
          background: url(${!classSet ? child_bg[1] : child_bg[0]});
          background-size: 100% 100%;
          background-repeat: no-repeat;`"
  >
    <!-- min-height:${data.data.length > 0 ? '6.4375rem' : '5.125rem'};
          width: ${data.data.length > 0 ? '11.5rem' : '8.75rem'}; -->
    <div class="content">
      <div class="content-item-title">{{ data.title }}</div>
      <div class="content-item" v-for="item in data.data" :key="item.name">
        <div class="content-item-name">{{ item.name }}</div>
        <div>
          <span class="content-item-num">{{ item.value }}</span>
          <span class="content-item-unit">{{ item.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import tipsITem_child_bg0 from '../assets/images/tipsITemSmall_bg2_1.png';
  import tipsITem_child_bg1 from '../assets/images/tipsITemSmall_bg2.png';
  const child_bg = [tipsITem_child_bg0, tipsITem_child_bg1];
  defineProps({
    data: {
      type: Object as any,
      default: () => ({
        title: '好洋区',
        data: [
          // {
          //   name: '氨氮',
          //   value: '0.8',
          //   unit: 'mg/L',
          // },
        ],
        config: {
          theme: '',
        },
      }),
    },
    classSet: {
      type: Boolean,
      default: () => false,
    },
  });
  const handleClick = () => {
    // console.log('点击了');
    // emit('click');
  };
</script>
<style lang="less" scoped>
  .tipsITem_pd {
    padding: 0.5625rem;
    padding-bottom: 1.85rem;
  }

  .tipsITem_pd2 {
    padding: 0.625rem;
    padding-bottom: 1.65rem;
  }

  .tipsITem-component {
    // width: 8.75rem
    // height: 5.125rem
    width: 9.625rem;
    min-height: 4.75rem;
    background-size: 100% 100%;

    position: relative;

    .content {
      display: flex;
      flex-direction: column;

      .content-item-title {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: #ffffff;
        text-align: center;
      }

      .content-item {
        display: flex;
        align-items: center;
        justify-content: center;
        // justify-content: space-between;

        // &:last-child {
        margin-bottom: 0.3125rem;
        // }
        // background-image: url('../BI/assets/images/tipsITem_block.png');
        // background-size: 130px 101px;

        .content-item-num {
          font-family: PingFang SC;
          font-weight: 600;
          font-size: 0.875rem;
          color: #ffffff;
          line-height: 0.875rem;
        }

        .content-item-unit {
          // padding-left: 0.25rem;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 0.875rem;
          color: #ebebeb;
          line-height: 0.875rem;
        }

        .content-item-name {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 0.875rem;
          color: #ffffff;
          line-height: 0.875rem;
          display: flex;
          align-items: center;

          .child_img {
            margin-right: 0.5625rem;
            width: 1.75rem;
            height: 1.75rem;
          }
        }
      }
    }
  }
</style>
