<template>
  <BiCardBox title="氨氮">
    <template #content>
      <div class="ammonia-content" v-if="data.length">
        <div class="box-item" v-for="(item, index) in data" :key="item.indexCode">
          <div class="icon">
            <img :src="index % 2 ? icon5 : icon4" />
          </div>
          <div :class="['box', { box2: index % 2 }]">
            <div class="name">{{ item.name }}</div>
            <div :class="['item-value-unit', { type2: index > 2 }]">
              <div class="value" @click="openModal(item.indexCode, groupCode)">{{
                item.value !== '' && item.value !== null
                  ? Number(item.value).toFixed(item.digit)
                  : '-'
              }}</div>
              <div class="unit">{{ item.unit }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="empty-container w-full h-full" v-else>
        <Empty />
      </div>
    </template>
  </BiCardBox>
  <IndicatorModal
    v-model:open="indicatorOpen"
    :width="isIPCRef ? '100%' : '1272px'"
    :bodyStyle="modalBodyStyle"
    :destroyOnClose="true"
    :groupInfo="groupInfo"
    :themeColor="themeColor"
    :echartsConfig="echartsConfig"
    :multiple="multiple"
    :factoryId="factoryId"
    title="指标详情"
    :requestHeader="requestHeader"
    :footer="modalFooter"
    :base-url="baseUrl"
    wrapClassName="aoa-ipc-modal-curve"
  />
</template>

<script lang="ts" setup>
  import { ref, PropType, computed } from 'vue';
  import { BiCardBox, Empty } from './BiCard';
  import type { DataList } from '../type';
  import icon4 from '/@aoa/views/BI/assets/images/icon_4.png';
  import icon5 from '/@aoa/views/BI/assets/images/icon_5.png';

  import { IndicatorModal } from 'hlxb-business-ui';
  import { getProcessEditorTheme } from '/@process-editor/assets/theme';
  import { isIPCBreakPoint } from '/@/hooks/event/useBreakpoint';
  import { useWindowSize } from '@vueuse/core';
  import { getToken } from '/@/utils/auth';
  import { useDomain } from '/@/locales/useDomain';
  import { getFactoryId } from '/@aoa/utils/factory';
  import { getAppEnvConfig } from '/@/utils/env';

  defineProps({
    data: {
      type: Array as PropType<DataList[]>,
      default: () => [],
    },
    groupCode: String,
  });

  const { themeColor, echartsConfig } = getProcessEditorTheme();
  const factoryId = getFactoryId();
  const { isIPCRef } = isIPCBreakPoint();
  const { height } = useWindowSize();
  const modalBodyStyle = computed(() => {
    return isIPCRef.value
      ? {
          height: `${height.value - 64}px`,
        }
      : { height: '716px' };
  });
  const modalFooter = computed(() => {
    return isIPCRef.value ? null : ' ';
  });

  const baseUrl = computed(() => getAppEnvConfig().VITE_GLOB_API_URL);

  const { getTenantId } = useDomain();
  const requestHeader: any = {
    Authorization: getToken(),
    'Tenant-Id': getTenantId.value,
  };

  const indicatorOpen = ref(false);
  const groupInfo = ref({
    groupCode: '',
    resourceInterfaceId: '3',
    jsConvert: false,
    indexCodes: '',
  });
  const multiple = ref(false);

  const openModal = (code, groupCode) => {
    groupInfo.value.indexCodes = code;
    groupInfo.value.groupCode = groupCode;
    indicatorOpen.value = true;
  };
</script>
<style lang="less" scoped>
  .ammonia-content {
    padding: 10px 8px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    height: 100%;
    min-height: 175px;
    gap: 8px;

    .box-item {
      position: relative;
      width: calc(50% - 4px);
      height: 48px;
      display: flex;
      align-items: center;

      .icon {
        position: absolute;
        left: 0;
        top: 0;
        width: 42px;
        height: 100%;
        z-index: 1;

        img {
          width: 100%;
        }
      }

      .box {
        margin-left: 21px;
        padding: 5px 0 0 30px;
        width: calc(100% - 21px);
        height: calc(100% - 4px);
        border-radius: 4px;
        background-color: rgba(33, 121, 105, 0.32);
        box-shadow: inset 1px 2px 4px 0px rgba(1, 18, 15, 1),
          inset 0px -1px 4px 0px rgba(210, 255, 246, 1);
        overflow: hidden;

        &.box2 {
          background-color: rgba(25, 63, 91, 0.32);
          box-shadow: inset 1px 2px 4px 0px rgba(1, 12, 20, 1),
            inset 0px -1px 4px 0px rgba(225, 242, 255, 1);
        }
      }

      .name {
        font-size: 13px;
        color: #ebebeb;
        line-height: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .item-value-unit {
        padding-top: 4px;
        display: flex;
        align-items: flex-end;
        line-height: 1;

        .value {
          font-family: Alimama ShuHeiTi;
          font-weight: 500;
          font-size: 16px;
          cursor: pointer;
        }

        .unit {
          margin-bottom: 2px;
          padding-left: 4px;
          font-size: 13px;
        }
      }
    }
  }

  @media screen and (min-width: 1800px) {
    .ammonia-content {
      padding: 8px 20px;
      gap: 8px 20px;

      .box-item {
        width: calc(50% - 10px);

        .box {
          height: calc(100% - 2px);
        }
      }
    }
  }

  @media screen and (min-width: 2000px) {
    .ammonia-content {
      .px2vw(12);
      .px2vh(12);
      padding: @vh @vw;
      gap: @vh @vw;

      .box-item {
        .px2vw(6);
        width: calc(50% - @vw);
        .height-prop(48);

        .icon {
          .width-prop(44);
        }

        .box {
          .px2vw(44);
          .px2vh(6);
          .width-prop(22, margin-left);
          padding: @vh 0 0 calc(@vw / 2 + 12px);
          width: calc(100% - @vw / 2);
          height: calc(100% - 2px);
        }

        .name {
          .font-size(14);
        }

        .item-value-unit {
          .height-prop(4, padding-top);

          .value {
            .font-size(18);
          }

          .unit {
            .height-prop(2, margin-bottom);
            .width-prop(8, padding-left);
            .font-size(14);
          }
        }
      }
    }
  }
</style>
