<template>
  <div class="frame-layout-container" v-if="showFrame">
    <template v-for="(frame, index) in getDoorFramePages" :key="frame.path">
      <FramePage
        v-if="frame.meta.frameSrc && hasRenderFrame(frame) && resultParams?.[index]?.dvId"
        :style="getIframeStyle(frame)"
        :pageType="frame.meta.modelClassify === 4 ? 'door' : ''"
        :frameSrc="getFrameSrcDoorFn(frame)"
        :open="showIframe(frame)"
      />
    </template>
    <template v-for="frame in getFramePages" :key="frame.path">
      <FramePage
        v-if="frame.meta.frameSrc && hasRenderFrame(frame)"
        :style="getIframeStyle(frame)"
        :frameSrc="getFrameSrcFn(frame)"
        :open="showIframe(frame)"
      />
    </template>
  </div>
</template>
<script lang="ts">
  import { defineComponent, unref, ref, CSSProperties, onMounted, onUnmounted } from 'vue';
  import QueryString from 'qs';
  import FramePage from '/@/views/sys/iframe/index.vue';
  import { getToken } from '/@/utils/auth';
  import { TENANTID_KEY, FACTORY_KEY } from '/@/enums/cacheEnum';
  import { useUserStoreWithOut } from '/@/store/modules/user';
  import { createLocalStorage } from '/@/utils/cache';
  import { useRoute } from 'vue-router';
  import { useFrameKeepAlive } from './useFrameKeepAlive';
  import { getParamKeyApi } from '/@/api/admin/param';
  import type { AppRouteRecordRaw } from '/@/router/types';
  import { getPortalIdByModulelId } from '/@/api/database/portal';
  import { ParamsKeyEnum } from '/@/enums/appEnum';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useEventListener } from '/@/hooks/event/useEventListener';
  import { cloneDeep } from 'lodash-es';
  import { useGo } from '/@/hooks/web/usePage';

  const ls = createLocalStorage();
  const tenantId = ls.get(TENANTID_KEY) || '';
  const { createMessage } = useMessage();

  export default defineComponent({
    name: 'FrameLayout',
    components: { FramePage },
    setup() {
      const go = useGo();
      const userStore = useUserStoreWithOut();

      const resultParams = ref<{ modelId: string; dvId: string }[]>([]);

      let { getFramePages: _getFramePages_, hasRenderFrame, showIframe } = useFrameKeepAlive();
      const getFramePages = ref<any[]>([]);
      const getDoorFramePages = ref<any[]>([]);
      const warehouseToken = ref('');

      const _route_ = useRoute();
      const removeEventFn = ref();
      const showFrame = ref(false);

      /**
       * 获取相对地址的前缀
       * @param {string} systemParams 系统参数
       */
      async function getPrefix(systemParams: string) {
        const prefixUrl = await getParamKeyApi(String(systemParams));

        return prefixUrl || '';
      }

      /**
       * 处理iframe地址(相对地址需要前缀)
       * @param {AppRouteRecordRaw[]} data 路由数组
       */
      async function handleIframePrefixUrl(data: AppRouteRecordRaw[]) {
        const result = data;
        if (!result.length) return result;

        for (let i = 0; i < result.length; i++) {
          if (!result[i].meta.systemParams) continue;
          const prefixUrl = await getPrefix(result[i].meta.systemParams as string);
          result[i].meta.frameSrc = `${prefixUrl}${result[i].meta.frameSrc}`;
        }
        return result;
      }

      /**
       * 获取iframe的完整地址(拼接参数)
       * @param {string} src iframe地址
       */
      function getFrameSrcFn(frame: AppRouteRecordRaw) {
        const src = frame.meta.frameSrc || '';
        let path = '';
        // http://120.79.95.202/web/demo/goto.aspx?Port=10081&Folder=process-preview?flowId=13
        if (src.includes('/web/demo/goto.aspx')) {
          const source = src.split('&Folder=');
          const routeParams = source[1].split('?');
          const route = routeParams[0];
          const query = routeParams[1];
          const url = `${source[0]}&Folder=${route}`;

          const queryObj = QueryString.parse(query);
          if (_route_.meta.flowId) {
            queryObj['flowId'] = String(_route_.meta.flowId);
          }
          const params = Object.assign(
            {},
            {
              token: getToken(),
              ...queryObj,
              tenantId,
              isIframe: true,
            },
          );
          path = url + encodeURIComponent(QueryString.stringify(params, { addQueryPrefix: true }));
        } else {
          const source = src.split('?');
          const url = source[0];
          const query = source[1];
          const queryObj = QueryString.parse(query);
          if (_route_.meta.flowId) {
            queryObj['flowId'] = String(_route_.meta.flowId);
          }
          let result: CSSProperties = { display: showIframe(frame) ? 'block' : 'none' };
          const params = Object.assign(
            {},
            {
              token: queryObj['isSingleLogin'] ? undefined : getToken(),
              access_token:
                queryObj['zhcz_warehouse'] && warehouseToken.value
                  ? warehouseToken.value
                  : undefined,
              ...queryObj,
              tenantId,
              isIframe: true,
              display: queryObj['path'] === 'index' ? { ...result } : undefined,
            },
          );

          path = url + QueryString.stringify(params, { addQueryPrefix: true });
        }

        return path;
      }

      // 拼接动态参数，水厂id
      function getFrameSrcDoorFn(frame) {
        const src = frame.meta.frameSrc || '';
        let path = '';
        const ls = createLocalStorage();
        const factoryId = ls.get(FACTORY_KEY) || userStore.getCurrentFactoryId;
        path = src + `&factoryId=${factoryId}`;
        return path;
      }

      function getPosition(frame: AppRouteRecordRaw): CSSProperties {
        const show = showIframe(frame);

        return {
          position: show ? 'relative' : 'absolute',
          left: show ? '0' : '-9999px',
          top: show ? '0' : '-9999px',
        };
      }

      /**
       * 获取iframe的切换样式
       * @param {AppRouteRecordRaw} frame iframe配置
       */
      function getIframeStyle(frame: AppRouteRecordRaw): CSSProperties {
        // let result: CSSProperties = { display: showIframe(frame) ? 'block' : 'none' };
        const flag = showIframe(frame);
        let result: CSSProperties = { display: flag ? 'block' : 'none' };

        if (frame.meta.iframeSwitch) {
          switch (frame.meta.iframeSwitch) {
            case 'absolute':
              result = getPosition(frame);
              break;
            default:
              break;
          }
        }
        return result;
      }

      // 获取菜单信息
      const getMenuInfo = async (data: Indexable, index: number) => {
        const { modelId, portalId } = await getPortalIdByModulelId({
          moduleId: data.meta.moduleId,
        });

        const url = await getParamKeyApi(ParamsKeyEnum.DOOR_URL);

        if (!url) {
          createMessage.warning('请在系统参数中设置门户设计地址域名');
        }

        resultParams.value[index] = { dvId: portalId, modelId };

        try {
          init(url, index);
        } catch (error) {
          console.log('error3', error);
          throw error;
        }
      };

      // 获取仓库管理嵌入页面token
      const getWarehouseToken = async () => {
        warehouseToken.value = await getParamKeyApi('access_token');
      };

      const messageFn = function (e: Indexable = {}) {
        const { name, url, jumpType } = e.data || {};

        if (name === 'jump') {
          return window.open(url, jumpType || '_blank');
        }
        if (name === 'navigator-jump') {
          // 门户快捷导航跳转
          go(url);
          return;
        }
      };

      async function init(doorUrl: string, index: number) {
        const params = Object.assign(
          {},
          {
            token: getToken(),
            tenantId,
            ...resultParams.value[index],
            isIframe: true,
            _time: new Date().getTime(),
          },
        );
        // preview

        let src = '';
        const routeInfoAll = cloneDeep(getDoorFramePages.value);
        let preview = routeInfoAll[index].meta.frameSrc;
        if (doorUrl.includes('web/demo/goto.aspx')) {
          const query = QueryString.stringify(params, { addQueryPrefix: true });
          src = doorUrl += `&Folder=preview` + encodeURIComponent(query);
        } else {
          src = doorUrl + preview + QueryString.stringify(params, { addQueryPrefix: true });
        }

        getDoorFramePages.value[index].meta.frameSrc = src;
        const { removeEvent } = useEventListener({
          el: window,
          name: 'message',
          listener: messageFn,
        });
        removeEventFn.value = removeEvent;
      }

      onMounted(async () => {
        console.log('filterDoorData1', _getFramePages_);

        try {
          // 没有绑定模型的嵌入页面
          getFramePages.value = unref(_getFramePages_).filter(
            (frame) =>
              !Reflect.has(frame.meta, 'modelId') &&
              !(frame.meta.modelClassify === 4 || frame.meta.modelClassify === 5) &&
              frame.path &&
              !frame.meta.wujie,
          );

          //配置了系统参数的嵌入页面，需要拼接前缀地址
          getFramePages.value.length && (await handleIframePrefixUrl(getFramePages.value));

          console.log('filterDoorData2', getFramePages.value);

          // 绑定了模型并且是门户和大屏的嵌入页面
          getDoorFramePages.value = unref(_getFramePages_).filter(
            (frame) =>
              Reflect.has(frame.meta, 'modelId') &&
              (frame.meta.modelClassify === 4 || frame.meta.modelClassify === 5) &&
              frame.path &&
              !frame.meta.wujie,
          );

          //门户/大屏的嵌入页面，获取服务环境的url，拼接前缀地址
          if (getDoorFramePages.value.length) {
            for (let i = 0; i < getDoorFramePages.value.length; i++) {
              await getMenuInfo(getDoorFramePages.value[i], i);
            }
          }

          await getWarehouseToken();

          showFrame.value = !!unref(_getFramePages_).length && !!userStore.getToken;
        } catch (error) {
          console.log('error1', error);
          throw error;
        }
      });

      onUnmounted(() => {
        console.log('iframe卸载');
        removeEventFn.value && removeEventFn.value();
        removeEventFn.value = null;
        showFrame.value = false;
      });

      return {
        getFramePages,
        hasRenderFrame,
        showFrame,
        getDoorFramePages,
        resultParams,
        getIframeStyle,
        getFrameSrcFn,
        getFrameSrcDoorFn,
        showIframe,
      };
    },
  });
</script>
