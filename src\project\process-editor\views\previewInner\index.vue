<template>
  <div class="w-full h-full overflow-hidden">
    <ProcessDiagram :flowId="flowId" v-if="flowId" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useRoute } from 'vue-router';
  import ProcessDiagram from '/@process-editor/components/BasicProcessDiagram/index.vue';

  const route = useRoute();
  const flowId = ref(route.meta.flowId);
</script>

<style scoped></style>
