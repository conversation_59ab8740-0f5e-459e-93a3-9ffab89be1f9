.ant-select {
  span.anticon:not(.app-iconify) {
    font-size: 16px;
    margin-right: 2px;
    vertical-align: -0.2em !important;
  }

  .ant-select-selector {
    padding-left: 12px !important;
  }

  .ant-select-clear {
    width: 18px;
    height: 16px;
    margin-top: calc(-16px / 2);
  }

  &-multiple {
    .ant-select-selection-item {
      font-size: 12px;
      margin-top: 2px;
      margin-bottom: 2px;
      padding-inline-end: 8px;
      margin-inline-end: 8px;
      color: @theme-color;
      border: 1px solid @theme-color-12p;
      background-color: @theme-color-5p;

      .ant-select-selection-item-remove {
        span.anticon:not(.app-iconify) {
          font-size: 12px;
          color: @theme-color;
        }
      }
    }

    .ant-select-selection-search {
      margin-inline-start: 0;
    }
  }

  &-selection-item {
    color: @text-color-bold;
  }

  &-dropdown &-item-option {
    color: @text-color-bold;

    &-selected {
      background-color: @theme-color !important;
      color: #fff !important;
    }

    &-active:not(.ant-select-item-option-disabled) {
      background-color: @table3-row-hover-bg;
    }
  }

  &-dropdown {
    background-color: @aoa3-join-from-bg;
    border: 1px solid @aoa3-join-border;
    box-shadow: 0 1px 8px 0 #577572;

    .ant-select-item {
      .font-size(14);

      margin: 2px 0;
    }

    .ant-select-tree {
      background-color: transparent;

      &-switcher_close {
        color: @text-color-help !important;
      }

      &-switcher_open {
        color: @theme-color !important;
      }

      &-node-content-wrapper {
        &:hover {
          background-color: var(--theme-color-12p) !important;
        }
      }

      &-node-selected {
        background-color: var(--theme-color-12p) !important;
        color: var(--theme-color) !important;
      }

      &-switcher-icon {
        font-size: 14px !important;
      }
    }
  }

  &-disabled:not(.ant-select-customize-input) {
    .ant-select-selector {
      background-color: @input-bg-disabled-aoa3 !important;
      border-color: @input-bg-disabled-aoa3;
    }

    .ant-select-arrow {
      display: none;
    }
  }
}
