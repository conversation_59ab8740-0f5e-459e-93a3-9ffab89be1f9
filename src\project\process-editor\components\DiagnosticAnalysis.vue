<template>
  <div
    :class="['diagnostic-analysis-wrapper', { 'ani-analysis': open }]"
    ref="target"
    v-if="open"
    :style="getBindStyle"
    v-loading="loading"
    :placement="placement"
    @mouseleave="handleMouseLeave"
  >
    <div class="list-wrapper">
      <div class="title">诊断分析</div>
      <div class="content-wrapper">
        <template v-if="status === 2">
          <div class="content analysis">
            <div class="label">诊断内容：</div>
            <div class="value">
              {{ content.inspectContent }}
            </div>
          </div>
        </template>
        <template v-if="status === 1">
          <div class="content reason">
            <div class="label">异常原因：</div>
            <div class="value">
              {{ content.exceptionReason }}
            </div>
          </div>
          <div class="content suggestion">
            <div class="label">诊断建议：</div>
            <div class="value">
              {{ content.inspectAdvice }}
            </div>
          </div>
        </template>
      </div>
      <div class="footer"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { onClickOutside } from '@vueuse/core';
  import { getDiagnosisRecordByIndicatorCode } from '../api/index';
  // 枚举变量引用需优化
  import { expressionTypeEnum } from '../views/editor/components/PenProps/kit/kit.data';

  const props = defineProps({
    open: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      default: () => ({}),
      required: true,
    },
    position: {
      type: Object,
      default: () => ({
        left: 0,
        top: 0,
      }),
      required: true,
    },
  });

  defineEmits(['update:open']);

  const open = ref(false);

  // const open = computed({
  //   get() {
  //     return props.open;
  //   },
  //   set(value) {
  //     emit('update:open', value);
  //   },
  // });

  const target = ref<HTMLElement | null>(null);
  onClickOutside(target, () => {
    open.value = false;
  });

  const NORMAL = 'normal';
  const status = computed(() => {
    return props.data.scriptResult === NORMAL ? 2 : 1;
  });

  // 气泡弹框宽度
  const targetWidth = computed(() => {
    return target.value?.clientWidth || 350;
  });
  let popHeight = 400;
  let popMinHeight = 300;
  // 气泡弹框高度度
  const targetHeight = computed(() => {
    return status.value === 2 ? popMinHeight : popHeight;
  });

  function handleMouseLeave() {
    // open.value = false;
  }

  const _position_ = computed(() => {
    const { position } = props;
    const parentElement = target.value?.parentElement; //父级元素
    const targetWidthHalf = targetWidth.value / 2; //气泡弹框宽度的一半
    const targetHeightHalf = targetHeight.value / 2; //气泡弹框高度的一半
    const penWidthHalf = props.data.width / 2; //画笔宽度的一半
    const penHeightHalf = props.data.height / 2; //画笔高度的一半

    // 默认上
    let top = position.top - targetHeight.value - 8;
    let left = position.left - targetWidthHalf;

    if (top < 0) {
      // 下
      top = position.top + props.data.height + 16;
      left = position.left - targetWidthHalf;
    }

    if (left < 0) {
      // 右
      left = position.left + penWidthHalf + 8;
      top = position.top - targetHeightHalf + penHeightHalf;
    }

    const parentElementWidth = parentElement?.clientWidth || 0;

    if (position.left + targetWidth.value > parentElementWidth) {
      // 左
      left = position.left - penWidthHalf - targetWidth.value;
      top = position.top - targetHeightHalf + penHeightHalf;
    }

    return {
      top: `${top}px`,
      left: `${left}px`,
    };
  });

  const getBindStyle = computed(() => {
    return {
      ..._position_.value,
      height: status.value === 2 ? `${popMinHeight}px` : `${popHeight}px`,
    };
  });

  // 气泡弹框位置
  const placement = computed(() => {
    const top = Number(_position_.value.top.split('px')[0]);
    const left = Number(_position_.value.left.split('px')[0]);
    const { position } = props;
    const targetWidthHalf = targetWidth.value / 2; //气泡弹框宽度的一半
    const targetHeightHalf = targetHeight.value / 2; //气泡弹框高度的一半
    const penWidthHalf = props.data.width / 2; //画笔宽度的一半
    const penHeightHalf = props.data.height / 2; //画笔高度的一半

    if (top === position.top + props.data.height + 16 && left === position.left - targetWidthHalf) {
      return 'bottom';
    } else if (
      left == position.left + penWidthHalf + 8 &&
      top === position.top - targetHeightHalf + penHeightHalf
    ) {
      return 'right';
    } else if (
      left === position.left - penWidthHalf - targetWidth.value &&
      top === position.top - targetHeightHalf + penHeightHalf
    ) {
      return 'left';
    }

    return 'top';
  });

  const queryParams = computed(() => {
    const params = {
      // startDateTime: dayjs().startOf('days').format('YYYY-MM-DD HH:mm:ss'),
      // endDateTime: dayjs().endOf('days').format('YYYY-MM-DD HH:mm:ss'),
      indicatorCode: props.data.code || '',
      // type: status.value, // 1：报警；2：常规
      diagnosisType: props.data.scriptType === expressionTypeEnum.rule ? 1 : 2,
    };

    return params;
  });

  const loading = ref(false);
  const content = ref<Recordable>({
    exceptionReason: '',
    inspectAdvice: '',
    inspectContent: '',
  });
  // let index = 0;
  async function getData() {
    try {
      loading.value = true;
      const data = await getDiagnosisRecordByIndicatorCode(queryParams.value);
      content.value.exceptionReason = data?.exceptionReason || '';
      content.value.inspectAdvice = data?.inspectAdvice || '';
      content.value.inspectContent = data?.inspectContent || '';
      if (status.value === 2 && content.value.inspectContent) open.value = true;

      if (status.value === 1 && content.value.inspectAdvice && content.value.exceptionReason)
        open.value = true;
      // index++ % 2 === 0 && (open.value = true);
    } catch (error) {
      loading.value = false;
      console.log('error', error);
    } finally {
      loading.value = false;
    }
  }

  watch([() => props.open, () => props.position], (val) => {
    if (val[0]) {
      getData();
    } else {
      open.value = false;
    }
  });
</script>

<style lang="less" scoped>
  .diagnostic-analysis-wrapper {
    position: absolute;
    z-index: 100;
    width: 350px;
    border-radius: 4px;
    // background: linear-gradient(180deg, #0b3487 0%, #082a7b 100%);
    // box-shadow: inset 0px 50px 100px 0px rgba(23, 25, 146, 0.6),
    // inset 0px -50px 100px 0px rgba(23, 78, 146, 0.6);
    // border: 1px solid rgba(255, 255, 255, 0.32);
    opacity: 0;

    :deep(.full-loading) {
      background: transparent;
    }

    &.ani-analysis {
      animation: fadeIn 0.3s forwards;
    }

    &[placement='top'] {
      &::before {
        // 倒三角
        content: '';
        position: absolute;
        bottom: -12px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-top: 12px solid #11348b;
      }
    }

    &[placement='bottom'] {
      &::before {
        // 倒三角
        content: '';
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-bottom: 12px solid #11348b;
      }
    }

    &[placement='left'] {
      &::before {
        // 倒三角
        content: '';
        position: absolute;
        right: -12px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-left: 12px solid #11348b;
      }
    }

    &[placement='right'] {
      &::before {
        // 倒三角
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-right: 12px solid #11348b;
      }
    }

    .list-wrapper {
      width: 100%;
      height: 100%;

      .title {
        font-weight: 600;
        font-size: 14px;
        height: 60px;
        line-height: 1;
        padding-top: 28px;
        color: #ffffff;
        padding-left: 32px;
        background-size: 100% 100%;
        background-image: url('../assets/images/header-bg.png');
      }

      .content-wrapper {
        overflow: auto;
        height: calc(100% - 100px);
        padding: 6px 32px;
        background-size: 100% 100%;
        background-image: url('../assets/images/content-bg.png');
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none; /* 对于Chrome, Safari, Edge */
        }
      }

      .footer {
        height: 40px;
        background-size: 100% 100%;
        background-image: url('../assets/images/footer-bg.png');
      }

      .content {
        padding: 12px;
        width: 100%;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.12);
        font-size: 14px;
        color: #ffffff;
        box-shadow: inset 0px 0px 8px 0px #48b3ff;
        border: 1px solid #48b3ff;

        &:not(:last-child) {
          margin-bottom: 16px;
        }

        .label {
          margin-bottom: 4px;
          font-weight: 600;
        }

        .value {
          font-weight: 400;
          text-align: justify;
          white-space: pre-wrap;
        }
      }
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
</style>
