import dayjs from 'dayjs';
import type { DataList } from './type';

export const colorList = [
  ['rgba(26, 0, 170, 1)', 'rgba(26, 0, 170, 0.12)', 'rgba(26, 0, 170, 0)'],
  ['rgba(172, 195, 0, 1)', 'rgba(172, 195, 0, 0.12)', 'rgba(172, 195, 0, 0)'],
  ['rgba(0, 146, 136, 1)', 'rgba(0, 146, 136, 0.12)', 'rgba(0, 146, 136, 0)'],
  ['rgba(4, 116, 185, 1)', 'rgba(4, 116, 185, 0.12)', 'rgba(4, 116, 185, 0)'],
  ['rgba(0, 143, 199, 1)', 'rgba(0, 143, 199, 0.12)', 'rgba(0, 143, 199, 0)'],
  ['rgba(195, 117, 0, 1)', 'rgba(195, 117, 0, 0.12)', 'rgba(195, 117, 0, 0)'],
  ['rgba(195, 142, 0, 1)', 'rgba(195, 142, 0, 0.12)', 'rgba(195, 142, 0, 0)'],
  ['rgba(92, 89, 255, 1)', 'rgba(92, 89, 255, 0.12)', 'rgba(92, 89, 255, 0)'],
  ['rgba(172, 195, 0, 1)', 'rgba(172, 195, 0, 0.12)', 'rgba(172, 195, 0, 0)'],
  ['rgba(26, 0, 170, 1)', 'rgba(26, 0, 170, 0.12)', 'rgba(26, 0, 170, 0)'],
  ['rgba(62, 0, 170, 1)', 'rgba(62, 0, 170, 0.12)', 'rgba(62, 0, 170, 0)'],
];

export const indexCodeList = [
  {
    resourceInterfaceId: '12',
    groupCode: 'GYJKNEW_MDRJY',
    name: '未端溶解氧',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'GYJKNEW_ADSJ',
    name: '氨氮数据',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '1935511800297021441',
    groupCode: 'GYJKNEW_JSSJ',
    name: '进水数据',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'GYJKNEW_XDSJ',
    name: '硝氮数据',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '3',
    groupCode: 'GYJKNEW_DSDH', //GYJKNEW_DHTJ
    name: '吨水电耗统计',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'GYJKNEW_JSLL',
    name: '进水流量',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'GYJKNEW_HYQ',
    name: '好氧区',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'GYJKNEW_QYQ',
    name: '缺氧区',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '12',
    groupCode: 'GYJKNEW_YYQ',
    name: '厌氧区',
    jsConvert: true,
  },
  {
    resourceInterfaceId: '3',
    groupCode: 'GYJKNEW_CTDSDH',
    name: '吨水电耗统计-传统',
    jsConvert: true,
  },
];

export const lineChartIcon1 =
  'image://data:image/png;base64,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';
export const lineChartIcon2 =
  'image://data:image/png;base64,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';

// 中心流量mock
export const mockWaterFlowData: DataList[] = [
  {
    unit: 'm³/h',
    indexCode: 'MN_ZSSLLNEW',
    name: '总进水瞬时流量',
    value: '',
    digit: 0,
  },
  {
    unit: 'm³/h',
    indexCode: 'MUCT1.JS.MUCT1_SSLL_MINUTE_AVG',
    name: '1#进水瞬时流量',
    value: '',
    digit: 0,
  },
  {
    unit: 'm³/h',
    indexCode: 'MUCT1.JS.MUCT2_SSLL_MINUTE_AVG',
    name: '2#进水瞬时流量',
    value: '',
    digit: 0,
  },
  {
    unit: 'm³/h',
    indexCode: 'MUCT1.JS.MUCT3_SSLL_MINUTE_AVG',
    name: '3#进水瞬时流量',
    value: '',
    digit: 0,
  },
];

// 吨水电耗统计 mock
export const mockElectricityChartData = Array.from(
  { length: Number(dayjs().format('DD')) },
  (_, index) => {
    return {
      name: '吨水单耗',
      value: null,
      valueHB: null,
      digit: 2,
      unit: 'kWh/m³',
      indexCode: 'MN_JQBQ_DSDH_H_AVG',
      collectDateTime: dayjs().format(`YYYY-MM-${index + 1} 00:00:00`),
    };
  },
);
// 中心好氧缺氧数据
export const codeList = [
  // 1组
  [
    {
      name: '1#A组生化池好氧区',
      groupCode: 'ZXSHC_1ASHC_HYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '1#A组生化池缺氧区',
      groupCode: 'ZXSHC_1ASHC_QYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '1#A组生化池厌氧区',
      groupCode: 'ZXSHC_1ASHC_YYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
  ],
  [
    {
      name: '1#B组生化池好氧区',
      groupCode: 'ZXSHC_1BSHC_HYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '1#B组生化池缺氧区',
      groupCode: 'ZXSHC_1BSHC_QYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '1#B组生化池厌氧区',
      groupCode: 'ZXSHC_1BSHC_YYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
  ],
  // 2组
  [
    {
      name: '2#A组生化池好氧区',
      groupCode: 'ZXSHC_2ASHC_HYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '2#A组生化池缺氧区',
      groupCode: 'ZXSHC_2ASHC_QYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '2#A组生化池厌氧区',
      groupCode: 'ZXSHC_2ASHC_YYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
  ],
  [
    {
      name: '2#B组生化池好氧区',
      groupCode: 'ZXSHC_2BSHC_HYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '2#B组生化池缺氧区',
      groupCode: 'ZXSHC_2BSHC_QYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '2#B组生化池厌氧区',
      groupCode: 'ZXSHC_2BSHC_YYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
  ],
  // 3组
  [
    {
      name: '3#A组生化池好氧区',
      groupCode: 'ZXSHC_3ASHC_HYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '3#A组生化池缺氧区',
      groupCode: 'ZXSHC_3ASHC_QYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '3#A组生化池厌氧区',
      groupCode: 'ZXSHC_3ASHC_YYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
  ],
  [
    {
      name: '3#B组生化池好氧区',
      groupCode: 'ZXSHC_3BSHC_HYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '3#B组生化池缺氧区',
      groupCode: 'ZXSHC_3BSHC_QYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
    {
      name: '3#B组生化池厌氧区',
      groupCode: 'ZXSHC_3BSHC_YYQ',
      resourceInterfaceId: '12',
      jsConvert: true,
    },
  ],
];
const scaleWidth = window.innerWidth;
export const positionList = [
  [
    {
      position:
        scaleWidth < 1500
          ? ['33.98%', '27.4%']
          : scaleWidth > 1500 && scaleWidth < 1926
          ? ['38.98%', '23.4%']
          : scaleWidth > 1920 && scaleWidth < 2566
          ? ['38.98%', '30.4%']
          : ['38.98%', '30.4%'],
    },
    {
      position:
        scaleWidth < 1500
          ? ['66.47%', '55.2%']
          : scaleWidth > 1500 && scaleWidth < 1926
          ? ['63.635%', '54.233%']
          : scaleWidth > 1920 && scaleWidth < 2566
          ? ['64.635%', '56.233%']
          : ['64.635%', '56.233%'],
    },
    {
      position:
        scaleWidth < 1500
          ? ['80%', '41.159%']
          : scaleWidth > 1500 && scaleWidth < 1926
          ? ['74%', '38.159%']
          : scaleWidth > 1920 && scaleWidth < 2566
          ? ['77%', '40.159%']
          : ['77%', '40.159%'],
    },
  ],
];
