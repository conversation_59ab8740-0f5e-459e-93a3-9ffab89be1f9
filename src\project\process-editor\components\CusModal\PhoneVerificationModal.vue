<template>
  <BasicModal
    :canFullscreen="false"
    :keyboard="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    :afterClose="afterClose"
    wrapClassName="phone-verification-modal-dark aoa3"
    width="400px"
  >
    <template #closeIcon> <IconButton color="#666666" icon="icon-park-outline:close" /> </template>

    <BasicForm @register="registerForm">
      <template #verificationCodeSlot="{ model, field }">
        <div class="verification-code-input">
          <a-input
            v-model:value="model[field]"
            placeholder="请输入验证码"
            :maxlength="4"
            style="width: calc(100% - 100px); margin-right: 8px"
          />
          <a-button
            type="primary"
            size="small"
            :disabled="countdown > 0"
            @click="handleSendCode"
            style="width: 92px; height: 32px"
          >
            {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
          </a-button>
        </div>
      </template>
    </BasicForm>

    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">确认</a-button>
    </template>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getPhoneVerificationSchemas } from './phoneData';
  import { controlSendSmsApi, controlVerifySmsCodeApi } from '/@process-editor/api/index';
  import { getParamKeyApi } from '/@/api/admin/param';
  import { SYSTEM_PARAMS } from '/@process-editor/constant/index';
  import { IconButton } from '/@/components/Button';
  import { useUserStore } from '/@/store/modules/user';
  const userStore = useUserStore();

  let title = ref('手机验证码验证');
  const emit = defineEmits(['success', 'register', 'afterClose']);

  const { createMessage } = useMessage();

  // 倒计时状态
  const countdown = ref(0);
  let timer = null;

  // 开始倒计时
  const startCountdown = () => {
    countdown.value = 60;
    timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  };
  const defaultVerifyCode = ref('');
  onMounted(async () => {
    defaultVerifyCode.value = await getParamKeyApi(SYSTEM_PARAMS.smsCode);
  });

  const [registerForm, { resetFields, validateFields, getFieldsValue, setFieldsValue }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: getPhoneVerificationSchemas(),
    showActionButtonGroup: false,
  });

  const [registerModal, { closeModal }] = useModalInner(async () => {
    // 可以在这里设置初始值或获取用户手机号
    resetFields();
    if (defaultVerifyCode.value) {
      setFieldsValue({
        verificationCode: defaultVerifyCode.value,
      });
    }
    if (userStore.getUserInfo?.phone) {
      setFieldsValue({
        phoneNumber: userStore.getUserInfo?.phone,
      });
    }
  });

  // 发送验证码
  const sendVerificationCode = async (phoneNumber: string) => {
    try {
      // 调用发送验证码的API - 根据项目实际API路径调整
      await controlSendSmsApi({ rcvPhone: phoneNumber });
      createMessage.success('验证码已发送');
      return true;
    } catch (error) {
      createMessage.error('验证码发送失败');
      return false;
    }
  };

  const okLoading = ref(false);
  async function handleSubmit() {
    try {
      okLoading.value = true;
      await validateFields();
      const values = getFieldsValue();
      const { phoneNumber, verificationCode } = values;
      // 验证验证码
      await controlVerifySmsCodeApi({
        rcvPhone: phoneNumber,
        authCode: verificationCode,
      });
      // createMessage.success(data || '验证成功');
      emit('success', values);
      handleCancel();
    } catch (error) {
      console.error('验证失败:', error);
    } finally {
      okLoading.value = false;
    }
  }

  function handleCancel() {
    closeModal();
    resetFields();
    timer && clearInterval(timer);
    countdown.value = 0;
  }

  // 暴露发送验证码方法给表单使用
  const handleSendCode = async () => {
    try {
      const values = getFieldsValue();
      if (!values.phoneNumber) {
        createMessage.warning('请先输入手机号');
        return;
      }

      // 简单的手机号验证
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(values.phoneNumber)) {
        createMessage.warning('请输入正确的手机号');
        return;
      }

      const success = await sendVerificationCode(values.phoneNumber);
      if (success) {
        startCountdown();
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
    }
  };

  function afterClose() {
    emit('afterClose');
  }
</script>

<style lang="less"></style>
