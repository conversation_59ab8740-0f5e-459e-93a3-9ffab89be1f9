<template>
  <BasicModal
    :canFullscreen="false"
    :keyboard="false"
    @cancel="handleCancel"
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    wrapClassName="control-setting-modal-dark aoa3"
    width="30%"
  >
    <template #closeIcon> <IconButton color="#666666" icon="icon-park-outline:close" /> </template>

    <BasicForm @register="registerForm" />

    <template #footer>
      <a-button @click="handleCancel" class="default-btn">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="okLoading">保存</a-button>
    </template>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { schemas, timeUnitOptions } from './data';
  import { IconButton } from '/@/components/Button';
  import { sumoDistributeInfoApi, updateSumoDistributeApi } from '/@process-editor/api/index.ts';
  // import { Tag, Select, SelectOption } from 'ant-design-vue';
  // import { parseJson } from '/@process-editor/utils/index';
  import { pick } from 'lodash-es';

  let title = ref('下控设置');
  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();

  const [registerForm, { setFieldsValue, resetFields, validateFields, getFieldsValue }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas,
    showActionButtonGroup: false,
  });

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    console.log('-------> modal data', data);
    // let { params } = data;
    // params = parseJson(params);
    let controlSettingData = (await getControlSettingData()) || {};
    controlSettingData = pick(controlSettingData, [
      'intervalTimeTotal',
      'intervalTimeUnit',
      'stopTimeTotal',
      'stopTimeUnit',
    ]);

    setFieldsValue({ ...controlSettingData });
  });

  async function saveData(data) {
    try {
      await validateFields();
      let dataIntervalMultiple =
        timeUnitOptions.find((item) => item.value === data.intervalTimeUnit)?.multiple || 1;
      let totalSimulationTimeMultiple =
        timeUnitOptions.find((item) => item.value === data.stopTimeUnit)?.multiple || 1;
      let par = {
        ...data,
        dataInterval: data.intervalTimeTotal * dataIntervalMultiple,
        totalSimulationTime: data.stopTimeTotal * totalSimulationTimeMultiple,
      };

      await updateSumoDistributeApi(par);

      createMessage.success('操作成功');
      emit('success', data);
      handleCancel();
    } finally {
      okLoading.value = false;
    }
  }

  const okLoading = ref(false);
  async function handleSubmit() {
    const values = getFieldsValue();
    saveData({ ...values });
  }

  function handleCancel() {
    closeModal();
    resetFields();
  }

  async function getControlSettingData() {
    let res;
    try {
      res = await sumoDistributeInfoApi({});
    } catch (error) {
      console.log('-------> error', error);
    }
    return res;
  }
</script>

<style lang="less">
  /**
  .control-setting-modal-dark {
    color: #ffffff;

    //.ant-tag {
    //    color: white;
    //}

    .ant-modal-content {
      background: linear-gradient(180deg, #001f67 1%, #003372 100%);
      border-radius: 4px 4px 4px 4px;
      border: 2px solid #3a62a1;
    }

    .ant-modal-close {
      .vben-icon-button {
        span {
          color: #ffffffcc !important;
        }

        &:hover {
          background-color: transparent;
        }
      }
    }

    .ant-modal-header {
      background-color: transparent;

      .vben-basic-title {
        color: #ffffff;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .ant-modal-body {
      background-color: transparent;
    }

    .ant-modal-footer {
      background-color: transparent;
      color: #ffffff;

      .ant-btn:nth-child(1) {
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.4);
        background-color: transparent !important;
        color: #ffffff !important;
      }

      .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-second):not(.ant-btn-error):not(
          .ant-btn-dangerous
        ):not([disabled]):hover {
        &:hover {
          background-color: transparent !important;
        }
      }

      .ant-btn:nth-child(2) {
        border-radius: 4px;
        background-color: #0b62cb !important;
        color: #ffffff !important;

        border: 1px solid rgba(255, 255, 255, 0.4);
      }
    }

    .ant-form {
      label {
        color: #ffffff !important;
      }

      .ant-input-number {
        background-color: transparent !important;
        border: 1px solid rgba(255, 255, 255, 0.4) !important;

        .ant-input-number-input {
          color: white;
        }

        .ant-input-number-handler-wrap {
          background-color: transparent;

          .ant-input-number-handler-down-inner,
          .ant-input-number-handler-up-inner {
            color: white;
          }

          .ant-input-number-handler {
            border-color: rgba(255, 255, 255, 0.4);
          }
        }
      }

      .ant-input-number-disabled {
        background-color: #f2f3f52f !important;
      }

      .ant-select {
        .ant-select-selector {
          border: 1px solid rgba(255, 255, 255, 0.4) !important;
          background: transparent !important;

          .ant-select-selection-item {
            color: white;
          }
        }

        .ant-select-clear {
          color: rgba(255, 255, 255, 0.4);
          background-color: #00286c !important;

          &:hover {
            color: rgba(255, 255, 255, 0.4);
          }
        }
      }
    }

    .ant-radio-button-wrapper {
      background-color: transparent;
      border: 1px solid rgba(255, 255, 255, 0.4);
    }
  }

  .control-setting-modal-select-dropdown {
    background-color: #132b4e !important;

    .ant-select-item-option {
      color: #fff !important;
      background: unset !important;

      &:hover {
        background: rgb(43 99 161 / 5%);
      }
    }

    .ant-select-item-option-active {
      background: rgba(255, 255, 255, 0.16) !important;
    }

    .ant-select-item-option-selected {
      background: rgba(11, 98, 203, 0.88) !important;
    }
  }
  */
</style>
