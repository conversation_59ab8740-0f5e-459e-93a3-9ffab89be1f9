// 场景分组指标树类型文件
export type SenceGroupIndicatorTree = Array<{
  id: string;
  label: string;
  value: string;
  children: Array<{
    id: string;
    parentId: string;
    weight: number;
    name: string | null;
    label: string;
    value: string;
    defaultInterfaceId: string | null;
    groupCode: string;
    indicators: Array<{
      name: string;
      code: string;
      unit: string | null;
      tag: string | null;
      maxVal: number | null;
      minVal: number | null;
      digit: number | null;
    }>;
  }>;
}>;

/**
 * 数据集类型定义
 * @description 用于定义数据集的结构
 * @interface DataSetType
 */

export type DataSetType = Array<{
  id: string;
  label: string;
  value: string;
  parentId?: string;
  weight: number;
  children: Array<{
    id: string;
    label: string;
    value: string;
    parentId: string;
    weight: number;
    name: string;
    keys: Array<{
      id: string;
      originName: string;
      label: string;
      isIndexValue: boolean;
    }>;
    values: Array<{
      unit: string;
      displayName: string;
      [property: string]: string;
    }>;
  }>;
}>;
