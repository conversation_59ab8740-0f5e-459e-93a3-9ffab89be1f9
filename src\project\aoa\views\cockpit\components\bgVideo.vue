<template>
  <div class="bg_video" ref="containerRef">
    <img
      ref="bgImgRef"
      class="bgImg"
      v-if="!imgFlag"
      :src="getEevReturnDomain('/oss/platform/dataSence/1/temp/baoqibg2.png')"
      alt=""
    />
    <img class="bgImg z_index" :src="!classSet ? baoqibg4 : baoqibg5" alt="" />
    <img
      ref="bgImgRef"
      class="bgImg"
      v-show="imgFlag"
      :src="getEevReturnDomain('/oss/platform/dataSence/1/temp/facility-static-bg.png')"
      @load="imgFlag = true"
      alt=""
      srcset=""
    />
    <template v-if="imgFlag">
      <div
        v-for="(item, index) in filterMasks"
        :key="'filter-' + index"
        :style="getFilterStyle(item)"
        @mouseenter="setHover(index, true)"
        @mouseleave="setHover(index, false)"
        @click="setIndex(index)"
        :class="[
          'filter_img_box',
          {
            img_active: (index === clickIndex && hoverShow) || (index === hoverIndex && hoverShow),
          },
          { bg_show: innerWidth < 1500 || innerWidth > 1926 },
        ]"
      >
        <img
          class="filterImg"
          :src="clickIndex === index ? item.url : index === hoverIndex ? item.url_h : item.url_h"
          style="width: 100%; height: 100%"
          alt="空"
        />
        <template v-if="index === clickIndex && hoverShow">
          <tipsItem
            :classSet="classSet"
            :indexc="tipIndex"
            v-for="(tip, tipIndex) in tipsItemPositions[index]"
            :key="'tip-template-' + tipIndex"
            :class="['tipsItem', `tipsItem_${tipIndex}`]"
            :data="tipsItemData[tipIndex].data"
            :style="getTipStyle(tip, item, tipIndex)"
            @sizeChange="handleTipsItemSizeChange($event, tipIndex)"
          />
        </template>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted, nextTick, CSSProperties } from 'vue';
  import tipsItem from './tipsItem.vue';
  import { getParams } from '/@aoa/utils';
  // import baoqibg1 from '../assets/images/baoqibg1.png';
  // import baoqibg2 from '../assets/images/baoqibg2.png';
  // import baoqibg3 from '../assets/images/baoqibg3.gif';
  // import baoqibg3 from '../assets/images/baoqibg3.png';
  // import baoqibg3 from '../assets/images/facility-static-bg.png';
  import { getEevReturnDomain } from '/@equipment-maintenance/utils';
  import baoqibg4 from '../assets/images/baoqibg4.png';
  import baoqibg5 from '../assets/images/baoqibg5.png';
  import filter1 from '../assets/images/filtersImg/filter1.png';
  import filter2 from '../assets/images/filtersImg/filter2.png';
  import filter3 from '../assets/images/filtersImg/filter3.png';
  import filter4 from '../assets/images/filtersImg/filter4.png';
  import filter5 from '../assets/images/filtersImg/filter5.png';
  import filter6 from '../assets/images/filtersImg/filter6.png';
  import filter1_h from '../assets/images/filtersImg/filter1_h.png';
  import filter2_h from '../assets/images/filtersImg/filter2_h.png';
  import filter3_h from '../assets/images/filtersImg/filter3_h.png';
  import filter4_h from '../assets/images/filtersImg/filter4_h.png';
  import filter5_h from '../assets/images/filtersImg/filter5_h.png';
  import filter6_h from '../assets/images/filtersImg/filter6_h.png';
  import { callResourceFunction } from '/@/api/config-center/scenes-group';
  const innerWidth = window.innerWidth;
  const baseWidth = 1920;
  const baseHeight = 1080;
  const containerRef = ref<HTMLElement | null>(null);
  const bgImgRef = ref<HTMLImageElement | null>(null);
  const imgRect = ref({ left: 0, top: 0, width: baseWidth, height: baseHeight });
  const imgFlag = ref<boolean>(false);
  const tipsItemSize = ref<{ height: number; width: number }[]>([
    { height: 0, width: 0 },
    { height: 0, width: 0 },
    { height: 0, width: 0 },
  ]);
  const emit = defineEmits(['imgSize']);
  function handleTipsItemSizeChange(size: { width: number; height: number }, index: number) {
    console.log('index:number', index);
    tipsItemSize.value[index] = size;
  }

  const updateImgRect = () => {
    if (bgImgRef.value && containerRef.value) {
      const containerRect = containerRef.value.getBoundingClientRect();
      const img = bgImgRef.value;
      const imgNaturalWidth = img.naturalWidth || baseWidth;
      const imgNaturalHeight = img.naturalHeight || baseHeight;
      const containerWidth = containerRect.width;
      const containerHeight = containerRect.height;
      const scale = Math.min(containerWidth / imgNaturalWidth, containerHeight / imgNaturalHeight);
      const imgWidth = imgNaturalWidth * scale;
      const imgHeight = imgNaturalHeight * scale;
      const left = (containerWidth - imgWidth) / 2;
      const top = (containerHeight - imgHeight) / 2;
      imgRect.value = { left, top, width: imgWidth, height: imgHeight };
      emit('imgSize', imgRect.value);
    }
  };

  onMounted(() => {
    nextTick(() => {
      updateImgRect();
      window.addEventListener('resize', updateImgRect);
      if (bgImgRef.value) {
        bgImgRef.value.onload = updateImgRect;
      }
    });
    setIndex(5);
  });
  onUnmounted(() => {
    window.removeEventListener('resize', updateImgRect);
  });

  // 以1920x1080为基准的遮罩和tipsItem数据
  const filterMasks = [
    { left: 1014, top: 280, width: 458, height: 86, url: filter6, url_h: filter6_h, rotate: 24 },
    { left: 915, top: 335, width: 473, height: 93, url: filter5, url_h: filter5_h, rotate: 26 },
    { left: 810, top: 401, width: 474, height: 96, url: filter4, url_h: filter4_h, rotate: 27.1 },
    { left: 698, top: 467, width: 486, height: 107, url: filter3, url_h: filter3_h, rotate: 28 },
    { left: 577, top: 539, width: 491, height: 116, url: filter2, url_h: filter2_h, rotate: 30.5 },
    { left: 453, top: 609, width: 499, height: 123, url: filter1, url_h: filter1_h, rotate: 33 },
  ];

  // tipsItem的基准位置
  const tipsItemPositions = [
    [
      { left: innerWidth < 1926 ? 0.1 : innerWidth < 2566 ? 0.3 : 0.2, top: 0.6 }, // 好氧区
      {
        left: innerWidth < 1930 ? 0.52 : innerWidth < 2566 ? 0.58 : 0.63,
        top: innerWidth < 1926 ? 0.4 : innerWidth < 2566 ? 0.9 : 0.99,
      }, // 缺氧区
      {
        left: innerWidth < 1430 ? 0.89 : innerWidth < 1930 ? 0.9 : innerWidth < 2566 ? 0.95 : 0.99,
        top: innerWidth < 1900 ? 0.1 : innerWidth > 1440 && innerWidth < 1926 ? 0.2 : 0.6,
      },
    ],
    [
      { left: innerWidth < 2500 ? 0.1 : 0.2, top: 0.7 }, // 好氧区
      {
        left: innerWidth < 1930 ? 0.52 : 0.6,
        top: innerWidth < 1926 ? 0.7 : innerWidth < 2560 ? 0.7 : 0.99,
      }, // 缺氧区
      {
        left: innerWidth < 1900 ? 0.87 : innerWidth < 1930 ? 0.9 : 0.96,
        top: innerWidth < 1900 ? 0.1 : innerWidth > 1390 && innerWidth < 1926 ? 0.2 : 0.4,
      },
    ],
    [
      { left: innerWidth < 2500 ? 0.1 : 0.2, top: 0.7 }, // 好氧区
      {
        left: innerWidth < 1930 ? 0.56 : 0.6,
        top: innerWidth < 1926 ? 0.7 : innerWidth < 2560 ? 0.7 : 0.99,
      }, // 缺氧区
      {
        left: innerWidth < 1446 ? 0.87 : innerWidth < 1930 ? 0.9 : innerWidth < 2560 ? 0.96 : 0.99,
        top: innerWidth < 1900 ? 0.1 : innerWidth > 1390 && innerWidth < 1926 ? 0.2 : 0.35,
      },
    ],
    [
      { left: innerWidth < 2500 ? 0.1 : 0.2, top: 0.7 }, // 好氧区
      {
        left: innerWidth < 1930 ? 0.56 : 0.6,
        top: innerWidth < 1926 ? 0.7 : innerWidth < 2560 ? 0.7 : 0.9,
      }, // 缺氧区
      {
        left: innerWidth < 1446 ? 0.87 : innerWidth < 1930 ? 0.9 : innerWidth < 2560 ? 0.96 : 0.99,
        top: innerWidth < 1900 ? 0.1 : innerWidth > 1390 && innerWidth < 1926 ? 0.2 : 0.35,
      },
    ],
    [
      { left: innerWidth < 2500 ? 0.1 : 0.2, top: 0.5 }, // 好氧区
      {
        left: innerWidth < 1930 ? 0.55 : innerWidth < 2560 ? 0.6 : 0.6,
        top: innerWidth < 1926 ? 0.7 : innerWidth < 2560 ? 0.6 : 0.9,
      }, // 缺氧区
      {
        left: innerWidth < 1446 ? 0.87 : innerWidth < 1930 ? 0.9 : innerWidth < 2560 ? 0.96 : 0.99,
        top: innerWidth < 1921 ? 0.1 : innerWidth < 2560 ? 0.2 : 0.3,
      },
    ],
    [
      { left: 0.1, top: 0.5 }, // 好氧区
      {
        left: innerWidth < 1930 ? 0.55 : innerWidth < 2560 ? 0.6 : 0.6,
        top: innerWidth < 1920 ? 0.7 : innerWidth < 2560 ? 0.7 : 0.9,
      }, // 缺氧区
      {
        left: innerWidth < 1446 ? 0.87 : innerWidth < 1930 ? 0.9 : innerWidth < 2560 ? 0.96 : 0.99,
        top: innerWidth < 1925 ? 0.12 : innerWidth > 1390 && innerWidth < 1926 ? 0.2 : 0.3,
      }, // 厌氧区
    ],
  ];

  // 新的等比缩放遮罩和tipsItem样式计算
  const getFilterStyle = (item: any): CSSProperties => {
    const scale = imgRect.value.width / baseWidth;
    return {
      position: 'absolute',
      left: imgRect.value.left + item.left * scale + 'px',
      top: imgRect.value.top + item.top * scale + 'px',
      width: item.width * scale + 'px',
      height: item.height * scale + 'px',
      transform: `rotate(${item.rotate}deg)`,
      zIndex: 11,
    };
  };
  // 以底部中间为锚点定位tipsItem
  const getTipStyle = (tip: any, filter: any, index: number): CSSProperties => {
    const scale = imgRect.value.width / baseWidth;
    const filterWidth = filter.width * scale;
    const filterHeight = filter.height * scale;
    // if (tipsItemSize.value.length < 3) return { position: 'absolute' };
    const tipWidth = tipsItemSize.value[index].width;
    const tipHeight = tipsItemSize.value[index].height;
    // console.log(
    //   'ItemSize.value.width height',
    //   tipsItemSize.value[index].width,
    //   tipsItemSize.value[index].height,
    //   index,
    //   tip,
    // );
    return {
      position: 'absolute',
      // left: tip.left * filterWidth + (0 - tipWidth) / 2 + 'px',
      // top: tip.top * filterHeight - tipHeight + 'px',
      left: tip.left * filterWidth + (0 - tipWidth) / 2 + 'px',
      top: tip.top * filterHeight - tipHeight + 'px',
      scale,
      transform: `rotate(-${filter.rotate}deg)`,
    };
  };

  const codeList = [
    // 1组
    [
      {
        name: '1#A组生化池好氧区',
        groupCode: 'ZXSHC_1ASHC_HYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '1#A组生化池缺氧区',
        groupCode: 'ZXSHC_1ASHC_QYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '1#A组生化池厌氧区',
        groupCode: 'ZXSHC_1ASHC_YYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
    ],
    [
      {
        name: '1#B组生化池好氧区',
        groupCode: 'ZXSHC_1BSHC_HYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '1#B组生化池缺氧区',
        groupCode: 'ZXSHC_1BSHC_QYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '1#B组生化池厌氧区',
        groupCode: 'ZXSHC_1BSHC_YYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
    ],
    // 2组
    [
      {
        name: '2#A组生化池好氧区',
        groupCode: 'ZXSHC_2ASHC_HYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '2#A组生化池缺氧区',
        groupCode: 'ZXSHC_2ASHC_QYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '2#A组生化池厌氧区',
        groupCode: 'ZXSHC_2ASHC_YYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
    ],
    [
      {
        name: '2#B组生化池好氧区',
        groupCode: 'ZXSHC_2BSHC_HYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '2#B组生化池缺氧区',
        groupCode: 'ZXSHC_2BSHC_QYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '2#B组生化池厌氧区',
        groupCode: 'ZXSHC_2BSHC_YYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
    ],
    // 3组
    [
      {
        name: '3#A组生化池好氧区',
        groupCode: 'ZXSHC_3ASHC_HYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '3#A组生化池缺氧区',
        groupCode: 'ZXSHC_3ASHC_QYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '3#A组生化池厌氧区',
        groupCode: 'ZXSHC_3ASHC_YYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
    ],
    [
      {
        name: '3#B组生化池好氧区',
        groupCode: 'ZXSHC_3BSHC_HYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '3#B组生化池缺氧区',
        groupCode: 'ZXSHC_3BSHC_QYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
      {
        name: '3#B组生化池厌氧区',
        groupCode: 'ZXSHC_3BSHC_YYQ',
        resourceInterfaceId: '12',
        jsConvert: true,
      },
    ],
  ];

  const props = defineProps({
    hoverShow: {
      type: Boolean,
      default: () => true,
    },
    classSet: {
      type: Boolean,
      default: () => false,
    },
  });
  const tipsItemData = ref<any>([
    {
      data: {
        title: '好氧区',
        data: [
          { name: '氨氮', value: '', unit: 'mg/L' },
          { name: '溶解氧', value: '', unit: 'mg/L' },
        ],
        config: { theme: '' },
      },
      rotate: -25,
      position: ['19', '10'],
    },
    {
      data: {
        title: '缺氧区',
        data: [
          { name: '硝氮', value: '', unit: 'mg/L' },
          { name: 'ORP', value: '', unit: 'mV' },
        ],
        config: { theme: '' },
      },
      rotate: -25,
      position: ['55', '10'],
    },
    {
      data: {
        title: '厌氧区',
        data: [{ name: 'ORP', value: '', unit: 'mV' }],
        config: { theme: '' },
      },
      rotate: -25,
      position: ['76.52', '30'],
    },
  ]);

  // 好氧区
  const getHaoYangData = async (index) => {
    const params = getParams(codeList[index][0]);
    const res = await callResourceFunction(params);
    console.log('好氧区', res);
    tipsItemData.value[0].data.title = index % 2 === 0 ? 'B组-好氧区' : 'A组-好氧区';
    if (res && res.length) {
      tipsItemData.value[0].data.data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
    } else {
      tipsItemData.value[0].data.data = [
        {
          name: '氨氮',
          value: '',
          unit: 'mg/L',
        },
        {
          name: '溶解氧',
          value: '',
          unit: 'mg/L',
        },
      ];
    }
  };
  // 缺氧区
  const getQYangData = async (index) => {
    const params = getParams(codeList[index][1]);
    const res = await callResourceFunction(params);
    console.log('缺氧区', res);
    tipsItemData.value[1].data.title = index % 2 === 0 ? 'B组-缺氧区' : 'A组-缺氧区';
    if (res && res.length) {
      tipsItemData.value[1].data.data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
    } else {
      tipsItemData.value[1].data.data = [
        {
          name: '硝氮',
          value: '',
          unit: 'mg/L',
        },
        {
          name: 'ORP',
          value: '',
          unit: 'mV',
        },
      ];
    }
  };
  // 厌氧区
  const getYYangData = async (index) => {
    const params = getParams(codeList[index][2]);
    const res = await callResourceFunction(params);
    // console.log('厌氧区', res);
    tipsItemData.value[2].data.title = index % 2 === 0 ? 'B组-厌氧区' : 'A组-厌氧区';
    if (res && res.length) {
      tipsItemData.value[2].data.data = res.map((item) => {
        return {
          name: item.indexName,
          value: item?.value,
          digit: item?.digit ?? 2,
          unit: item.unitName,
          indexCode: item.indexCode,
        };
      });
    } else {
      tipsItemData.value[2].data.data = [
        // {
        //   name: '硝氮',
        //   value: '',
        //   unit: 'mg/L',
        // },
        {
          name: 'ORP',
          value: '',
          unit: 'mV',
        },
      ];
    }
  };
  const clickIndex = ref(5);
  const hoverIndex = ref(-1);
  function setIndex(index: number) {
    // console.log('index', index, innerWidth, tipsItemPositions[index]);
    if (!props.hoverShow) return;
    clickIndex.value = index;
    getHaoYangData(index);
    getQYangData(index);
    getYYangData(index);
  }
  function setHover(index: number, flag: boolean) {
    if (!props.hoverShow) return;
    if (flag) {
      hoverIndex.value = index;
    } else {
      hoverIndex.value = -1;
    }
  }
</script>
<style lang="less" scoped>
  .bg_video {
    position: absolute;
    height: 100%;
    width: 100%;
    // z-index: -1;
    top: 0;
    // object-fit: cover;
    // 使用 cover 让背景图覆盖容器，可能会裁切部分图片
    background: url('../assets/images/baoqibg1.png') no-repeat center center;
    background-size: cover;
    // background-size: 100% 100%;

    .bgImg {
      position: absolute;
      // object-fit: cover; /* 让图片不拉伸，通过裁切适配容器 */
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .z_index {
      z-index: 1;
    }

    .filter_img_box {
      cursor: pointer;
      z-index: 11;
      opacity: 0;
      transition: all 0.5s ease-in-out;

      @media screen and (max-width: 1920px) {
        // width: 481px;
        // height: 312.3px;
      }

      &.img_active {
        opacity: 1;
      }

      &.bg_show {
        .filterImg {
          // display: none;
        }
      }

      &:hover {
        .tipsItem {
          opacity: 1;
        }
      }
    }

    .filterImg {
      // position: absolute;
      // top: 0;
      // left: 0;
      // width: auto;
      // height: auto;
      // filter: blur(10px);
    }

    .filter1 {
      // width: 481px;
      // height: 312.3px;
      // left: calc(471.5 / 1920);
      // top: calc(514 / 1080);
      // filter: blur(10px);
    }

    .filter2 {
      width: 472px;
      height: 287.5px;
      // filter: blur(10px);
    }

    .filter3 {
      width: 467px;
      height: 271px;
      // filter: blur(10px);
    }

    .filter4 {
      width: 458.5px;
      height: 251.5px;
      // filter: blur(10px);
    }

    .filter5 {
      width: 451.5px;
      height: 238px;
      // filter: blur(10px);
    }

    .filter6 {
      width: 445px;
      height: 222.5px;
      // filter: blur(10px);
    }

    #bg_video {
      height: 100%;
      width: 100%;
      // object-fit: cover;
      background-color: transparent !important;
      pointer-events: none;

      .xgplayer-controls {
        display: none;
      }
    }
  }
</style>
